<template>
  <div class="yaml-editor">
    <div class="editor-header" v-if="showHeader">
      <div class="editor-title">
        <h4>{{ title }}</h4>
        <p v-if="description" class="editor-description">{{ description }}</p>
      </div>
      <div class="editor-actions">
        <button
          v-if="showTemplateButton"
          @click="showTemplateModal = true"
          class="btn btn-sm btn-secondary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z" />
          </svg>
          插入模板
        </button>
        <button
          @click="formatYaml"
          :disabled="!modelValue"
          class="btn btn-sm btn-secondary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
          </svg>
          格式化
        </button>
        <button
          @click="validateYaml"
          :disabled="!modelValue"
          class="btn btn-sm btn-secondary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          验证
        </button>
      </div>
    </div>

    <div class="editor-container" :style="{ height: editorHeight }">
      <div ref="editorRef" class="monaco-editor"></div>
    </div>

    <div v-if="validationErrors.length > 0" class="validation-errors">
      <div class="error-header">
        <svg class="error-icon" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
        </svg>
        <span>YAML 验证错误</span>
      </div>
      <ul class="error-list">
        <li v-for="(error, index) in validationErrors" :key="index" class="error-item">
          <span class="error-line">第 {{ error.line }} 行:</span>
          <span class="error-message">{{ error.message }}</span>
        </li>
      </ul>
    </div>

    <!-- 模板选择模态框 -->
    <div v-if="showTemplateModal" class="modal-overlay" @click="closeTemplateModal">
      <div class="modal-container" @click.stop>
        <div class="modal-header">
          <h3>选择 YAML 模板</h3>
          <button @click="closeTemplateModal" class="modal-close">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>

        <div class="template-grid">
          <div
            v-for="template in templates"
            :key="template.id"
            class="template-card"
            @click="insertTemplate(template)"
          >
            <div class="template-icon">
              <svg viewBox="0 0 20 20" fill="currentColor">
                <path :d="template.icon" />
              </svg>
            </div>
            <div class="template-info">
              <h4>{{ template.name }}</h4>
              <p>{{ template.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed, useTemplateRef } from 'vue'
import loader from '@monaco-editor/loader'
import yaml from 'js-yaml'
import type * as Monaco from 'monaco-editor'
import { YamlTemplate, ValidationError } from './types/YamlEditor'

interface Props {
  modelValue?: string
  title?: string
  description?: string
  height?: string
  showHeader?: boolean
  showTemplateButton?: boolean
  readonly?: boolean
  language?: string
  theme?: string
  templates?: YamlTemplate[]
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
  (e: 'validate', errors: ValidationError[]): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  title: 'YAML 编辑器',
  description: '',
  height: '400px',
  showHeader: true,
  showTemplateButton: true,
  readonly: false,
  language: 'yaml',
  theme: 'vs-dark',
  templates: () => []
})

const emit = defineEmits<Emits>()

// 响应式数据
const editorRef = useTemplateRef('editorRef')
const showTemplateModal = ref(false)
const validationErrors = ref<ValidationError[]>([])
let editor: Monaco.editor.IStandaloneCodeEditor | null = null
let monaco: typeof Monaco | null = null

// 计算属性
const editorHeight = computed(() => props.height)

// 默认模板
const defaultTemplates: YamlTemplate[] = [
  {
    id: 'ci-pipeline',
    name: 'CI 流水线',
    description: '持续集成流水线配置模板',
    icon: 'M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm6 5a1 1 0 10-2 0v6a1 1 0 102 0V7z',
    content: `name: CI Pipeline
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    - name: Install dependencies
      run: npm ci
    - name: Run tests
      run: npm test
    - name: Build
      run: npm run build`
  },
  {
    id: 'cd-pipeline',
    name: 'CD 流水线',
    description: '持续部署流水线配置模板',
    icon: 'M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm1 5a1 1 0 000 2h4.586l-2.293 2.293a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 10-1.414 1.414L9.586 7H5z',
    content: `name: CD Pipeline
on:
  workflow_run:
    workflows: ["CI Pipeline"]
    types:
      - completed

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: \${{ github.event.workflow_run.conclusion == 'success' }}
    steps:
    - uses: actions/checkout@v3
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment"
        # Add your deployment commands here
    - name: Run smoke tests
      run: |
        echo "Running smoke tests"
        # Add your smoke test commands here`
  },
  {
    id: 'docker-compose',
    name: 'Docker Compose',
    description: 'Docker Compose 服务配置模板',
    icon: 'M3 7v10a2 2 0 002 2h14l-2-2H5V7H3zM14 2H8a2 2 0 00-2 2v8a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2zM8 4h6v8H8V4z',
    content: `version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**********************************/myapp
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=myapp
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:`
  },
  {
    id: 'kubernetes-deployment',
    name: 'Kubernetes 部署',
    description: 'Kubernetes Deployment 配置模板',
    icon: 'M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z',
    content: `apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-app
  labels:
    app: my-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: my-app
  template:
    metadata:
      labels:
        app: my-app
    spec:
      containers:
      - name: my-app
        image: my-app:latest
        ports:
        - containerPort: 8080
        env:
        - name: NODE_ENV
          value: "production"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: my-app-service
spec:
  selector:
    app: my-app
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer`
  }
]

// 合并模板
const templates = computed(() => [...defaultTemplates, ...props.templates])

// 方法
const initializeEditor = async () => {
  if (!editorRef.value) return

  try {
    // 配置 Monaco Editor
    loader.config({
      paths: {
        vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs'
      }
    })

    const monacoInstance = await loader.init()
    monaco = monacoInstance

    // 创建编辑器实例
    editor = monacoInstance.editor.create(editorRef.value, {
      value: props.modelValue,
      language: props.language,
      theme: props.theme,
      readOnly: props.readonly,
      automaticLayout: true,
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      fontSize: 14,
      lineNumbers: 'on',
      roundedSelection: false,
      scrollbar: {
        vertical: 'visible',
        horizontal: 'visible'
      },
      folding: true,
      foldingStrategy: 'indentation',
      showFoldingControls: 'always',
      wordWrap: 'on',
      tabSize: 2,
      insertSpaces: true
    })

    // 监听内容变化
    editor.onDidChangeModelContent(() => {
      const value = editor?.getValue() || ''
      emit('update:modelValue', value)
      emit('change', value)
      
      // 自动验证
      if (value) {
        validateYamlContent(value)
      } else {
        validationErrors.value = []
      }
    })

    // 设置 YAML 语言配置
    monacoInstance.languages.setLanguageConfiguration('yaml', {
      brackets: [
        ['{', '}'],
        ['[', ']'],
        ['(', ')']
      ],
      autoClosingPairs: [
        { open: '{', close: '}' },
        { open: '[', close: ']' },
        { open: '(', close: ')' },
        { open: '"', close: '"' },
        { open: "'", close: "'" }
      ],
      surroundingPairs: [
        { open: '{', close: '}' },
        { open: '[', close: ']' },
        { open: '(', close: ')' },
        { open: '"', close: '"' },
        { open: "'", close: "'" }
      ]
    })

  } catch (error) {
    console.error('Failed to initialize Monaco Editor:', error)
  }
}

const validateYamlContent = (content: string) => {
  const errors: ValidationError[] = []
  
  try {
    yaml.load(content)
    validationErrors.value = []
  } catch (error: any) {
    if (error.mark) {
      errors.push({
        line: error.mark.line + 1,
        message: error.reason || error.message
      })
    } else {
      errors.push({
        line: 1,
        message: error.message || 'YAML 格式错误'
      })
    }
    validationErrors.value = errors
  }
  
  emit('validate', errors)
}

const formatYaml = () => {
  if (!editor || !props.modelValue) return

  try {
    const parsed = yaml.load(props.modelValue)
    const formatted = yaml.dump(parsed, {
      indent: 2,
      lineWidth: 120,
      noRefs: true
    })
    editor.setValue(formatted)
  } catch (error) {
    console.error('Failed to format YAML:', error)
  }
}

const validateYaml = () => {
  if (!props.modelValue) return
  validateYamlContent(props.modelValue)
}

const insertTemplate = (template: YamlTemplate) => {
  if (!editor || !monaco) return

  const position = editor.getPosition()
  if (position) {
    editor.executeEdits('insert-template', [{
      range: new monaco.Range(position.lineNumber, position.column, position.lineNumber, position.column),
      text: template.content
    }])
  } else {
    editor.setValue(template.content)
  }

  closeTemplateModal()
}

const closeTemplateModal = () => {
  showTemplateModal.value = false
}

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  if (editor && editor.getValue() !== newValue) {
    editor.setValue(newValue || '')
  }
})

// 生命周期
onMounted(async () => {
  await nextTick()
  await initializeEditor()
})

onUnmounted(() => {
  if (editor) {
    editor.dispose()
    editor = null
  }
})
</script>

<style scoped>
.yaml-editor {
  @apply border border-gray-300 rounded-lg overflow-hidden bg-white;
}

/* 编辑器头部 */
.editor-header {
  @apply flex items-center justify-between p-4 bg-gray-50 border-b border-gray-200;
}

.editor-title h4 {
  @apply text-lg font-semibold text-gray-900 m-0;
}

.editor-description {
  @apply text-sm text-gray-600 mt-1 m-0;
}

.editor-actions {
  @apply flex items-center gap-2;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-sm {
  @apply px-2 py-1 text-xs;
}

.btn-secondary {
  @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500;
}

.btn-icon {
  @apply w-4 h-4;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 编辑器容器 */
.editor-container {
  @apply relative overflow-hidden;
}

.monaco-editor {
  @apply w-full h-full;
}

/* 验证错误 */
.validation-errors {
  @apply bg-red-50 border-t border-red-200 p-4;
}

.error-header {
  @apply flex items-center gap-2 mb-2;
}

.error-icon {
  @apply w-5 h-5 text-red-500;
}

.error-header span {
  @apply text-sm font-medium text-red-800;
}

.error-list {
  @apply space-y-1 m-0 pl-7;
}

.error-item {
  @apply text-sm text-red-700;
}

.error-line {
  @apply font-medium;
}

.error-message {
  @apply ml-2;
}

/* 模态框样式 */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[80vh] overflow-hidden;
}

.modal-header {
  @apply flex items-center justify-between p-6 pb-4 border-b border-gray-200;
}

.modal-header h3 {
  @apply text-lg font-semibold text-gray-900 m-0;
}

.modal-close {
  @apply text-gray-400 hover:text-gray-600 p-1 rounded;
}

.modal-close svg {
  @apply w-5 h-5;
}

/* 模板网格 */
.template-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4 p-6 max-h-96 overflow-y-auto;
}

.template-card {
  @apply flex items-start gap-3 p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 cursor-pointer transition-colors;
}

.template-icon {
  @apply w-10 h-10 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center flex-shrink-0;
}

.template-icon svg {
  @apply w-5 h-5;
}

.template-info {
  @apply flex-1;
}

.template-info h4 {
  @apply text-sm font-semibold text-gray-900 m-0;
}

.template-info p {
  @apply text-xs text-gray-600 mt-1 m-0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-header {
    @apply flex-col items-start gap-3;
  }

  .editor-actions {
    @apply w-full justify-end;
  }

  .template-grid {
    @apply grid-cols-1;
  }

  .modal-container {
    @apply max-w-full mx-4;
  }
}

/* 动画效果 */
.modal-overlay {
  animation: fadeIn 0.2s ease-out;
}

.modal-container {
  animation: slideIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 悬停效果 */
.template-card:hover {
  @apply transform -translate-y-1;
}

.template-card:hover .template-icon {
  @apply bg-blue-200;
}

/* 深色主题适配 */
.yaml-editor.dark {
  @apply border-gray-600 bg-gray-800;
}

.yaml-editor.dark .editor-header {
  @apply bg-gray-700 border-gray-600;
}

.yaml-editor.dark .editor-title h4 {
  @apply text-gray-100;
}

.yaml-editor.dark .editor-description {
  @apply text-gray-300;
}

.yaml-editor.dark .btn-secondary {
  @apply bg-gray-600 text-gray-200 border-gray-500 hover:bg-gray-500;
}

.yaml-editor.dark .validation-errors {
  @apply bg-red-900 border-red-700;
}

.yaml-editor.dark .error-header span {
  @apply text-red-200;
}

.yaml-editor.dark .error-item {
  @apply text-red-300;
}
</style>
