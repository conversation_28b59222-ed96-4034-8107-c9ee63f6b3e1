<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <div class="header-info">
          <h2>CI 任务执行日志</h2>
          <div class="task-info">
            <span class="task-name">{{ task?.name }}</span>
            <StatusTag v-if="currentInstance" :status="currentInstance.status" />
          </div>
        </div>
        <button @click="$emit('close')" class="close-button">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <!-- 实例选择 -->
        <div class="instance-selector">
          <div class="selector-header">
            <h3>执行实例</h3>
            <div class="selector-actions">
              <button
                @click="refreshInstances"
                :disabled="loadingInstances"
                class="btn btn-sm btn-secondary"
              >
                <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
                刷新
              </button>
            </div>
          </div>
          
          <div class="instances-list">
            <div
              v-for="instance in instances"
              :key="instance.id"
              :class="['instance-item', { active: selectedInstanceId === instance.instanceId }]"
              @click="selectInstance(instance)"
            >
              <div class="instance-info">
                <div class="instance-id">{{ instance.instanceId }}</div>
                <div class="instance-time">
                  {{ formatTime(instance.startTime) }}
                  <span v-if="instance.endTime">
                    - {{ formatTime(instance.endTime) }}
                  </span>
                </div>
              </div>
              <div class="instance-status">
                <StatusTag :status="instance.status" />
              </div>
              <div class="instance-duration">
                {{ formatDuration(instance.startTime, instance.endTime) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 日志查看器 -->
        <div class="logs-section">
          <div class="logs-header">
            <h3>执行日志</h3>
            <div class="logs-actions">
              <label class="auto-scroll-toggle">
                <input
                  v-model="autoScroll"
                  type="checkbox"
                />
                <span>自动滚动</span>
              </label>
              <button
                @click="downloadLogs"
                :disabled="!currentInstance"
                class="btn btn-sm btn-secondary"
              >
                <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
                下载日志
              </button>
              <button
                @click="refreshLogs"
                :disabled="loadingLogs"
                class="btn btn-sm btn-secondary"
              >
                <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
                刷新日志
              </button>
            </div>
          </div>

          <div class="logs-viewer" ref="logsContainer">
            <div v-if="loadingLogs" class="logs-loading">
              <svg class="animate-spin" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
              </svg>
              加载日志中...
            </div>
            <div v-else-if="!currentLogs" class="logs-empty">
              请选择一个执行实例查看日志
            </div>
            <div v-else class="logs-content">
              <div
                v-for="(line, index) in logLines"
                :key="index"
                :class="['log-line', getLogLineClass(line)]"
              >
                <span class="log-timestamp">{{ extractTimestamp(line) }}</span>
                <span class="log-level">{{ extractLevel(line) }}</span>
                <span class="log-message">{{ extractMessage(line) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 执行控制 -->
        <div v-if="currentInstance && currentInstance.status === 'RUNNING'" class="execution-controls">
          <button
            @click="stopExecution"
            :disabled="stopping"
            class="btn btn-danger"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clip-rule="evenodd" />
            </svg>
            {{ stopping ? '停止中...' : '停止执行' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onUnmounted } from 'vue'
import StatusTag from '@/components/StatusTag.vue'
import { useCiStore } from '@/modules/devops/stores/ci'
import type { DevOpsCiTask, DevOpsCiTaskInstance } from '@/modules/devops/types/devops'

// Props
interface Props {
  visible: boolean
  task?: DevOpsCiTask | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

const ciStore = useCiStore()

// 响应式数据
const loadingInstances = ref(false)
const loadingLogs = ref(false)
const stopping = ref(false)
const autoScroll = ref(true)
const selectedInstanceId = ref<string>('')
const instances = ref<DevOpsCiTaskInstance[]>([])
const currentLogs = ref<string>('')
const logsContainer = ref<HTMLElement>()
const logRefreshInterval = ref<number>()

// 计算属性
const currentInstance = computed(() =>
  instances.value.find(instance => instance.instanceId === selectedInstanceId.value)
)

const logLines = computed(() => {
  if (!currentLogs.value) return []
  return currentLogs.value.split('\n').filter(line => line.trim())
})

// 方法
const handleOverlayClick = () => {
  emit('close')
}

const loadInstances = async () => {
  if (!props.task?.id) return

  loadingInstances.value = true
  try {
    // 使用实际的store方法获取任务实例
    const taskInstances = await ciStore.fetchTaskInstances(props.task.id)
    instances.value = taskInstances

    // 默认选择最新的实例
    if (instances.value.length > 0) {
      selectInstance(instances.value[0])
    }
  } catch (error) {
    console.error('加载任务实例失败:', error)
    // 如果API调用失败，使用模拟数据作为后备
    instances.value = [
      {
        id: 1,
        ciTaskId: props.task.id,
        instanceId: 'ci-20240115-001',
        status: 'COMPLETED',
        startTime: '2024-01-15T10:30:00Z',
        endTime: '2024-01-15T10:35:00Z',
        logs: '',
        userId: 1,
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-15T10:35:00Z'
      },
      {
        id: 2,
        ciTaskId: props.task.id,
        instanceId: 'ci-20240115-002',
        status: 'RUNNING',
        startTime: '2024-01-15T11:00:00Z',
        logs: '',
        userId: 1,
        createdAt: '2024-01-15T11:00:00Z',
        updatedAt: '2024-01-15T11:00:00Z'
      }
    ]

    if (instances.value.length > 0) {
      selectInstance(instances.value[0])
    }
  } finally {
    loadingInstances.value = false
  }
}

const refreshInstances = () => {
  loadInstances()
}

const selectInstance = (instance: DevOpsCiTaskInstance) => {
  selectedInstanceId.value = instance.instanceId
  loadLogs()
}

const loadLogs = async () => {
  if (!selectedInstanceId.value) return

  loadingLogs.value = true
  try {
    // 使用实际的store方法获取任务日志
    const logs = await ciStore.fetchTaskLogs(selectedInstanceId.value)
    currentLogs.value = logs

    if (autoScroll.value) {
      await nextTick()
      scrollToBottom()
    }
  } catch (error) {
    console.error('加载任务日志失败:', error)
    // 如果API调用失败，使用模拟数据作为后备
    currentLogs.value = `2024-01-15T10:30:00Z INFO 开始执行 CI 任务...
2024-01-15T10:30:05Z INFO 正在拉取代码...
2024-01-15T10:30:10Z INFO 代码拉取完成
2024-01-15T10:30:15Z INFO 开始安装依赖...
2024-01-15T10:31:30Z INFO 依赖安装完成
2024-01-15T10:31:35Z INFO 开始执行构建...
2024-01-15T10:32:00Z WARN 发现警告: 未使用的导入
2024-01-15T10:34:30Z INFO 构建完成
2024-01-15T10:34:35Z INFO 开始执行测试...
2024-01-15T10:35:00Z INFO 所有测试通过
2024-01-15T10:35:05Z INFO CI 任务执行成功`

    if (autoScroll.value) {
      await nextTick()
      scrollToBottom()
    }
  } finally {
    loadingLogs.value = false
  }
}

const refreshLogs = () => {
  loadLogs()
}

const downloadLogs = () => {
  if (!currentLogs.value || !currentInstance.value) return

  const blob = new Blob([currentLogs.value], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `ci-logs-${currentInstance.value.instanceId}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const stopExecution = async () => {
  if (!currentInstance.value) return

  stopping.value = true
  try {
    await ciStore.stopInstance(currentInstance.value.instanceId)
    // 刷新实例状态
    await loadInstances()
  } catch (error) {
    console.error('停止执行失败:', error)
  } finally {
    stopping.value = false
  }
}

const scrollToBottom = () => {
  if (logsContainer.value) {
    logsContainer.value.scrollTop = logsContainer.value.scrollHeight
  }
}

const formatTime = (time?: string) => {
  if (!time) return ''
  return new Date(time).toLocaleString()
}

const formatDuration = (startTime?: string, endTime?: string) => {
  if (!startTime) return ''
  
  const start = new Date(startTime)
  const end = endTime ? new Date(endTime) : new Date()
  const duration = end.getTime() - start.getTime()
  
  const minutes = Math.floor(duration / 60000)
  const seconds = Math.floor((duration % 60000) / 1000)
  
  if (minutes > 0) {
    return `${minutes}分${seconds}秒`
  }
  return `${seconds}秒`
}

const extractTimestamp = (line: string) => {
  const match = line.match(/^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z)/)
  return match ? match[1] : ''
}

const extractLevel = (line: string) => {
  const match = line.match(/\s(INFO|WARN|ERROR|DEBUG)\s/)
  return match ? match[1] : ''
}

const extractMessage = (line: string) => {
  const match = line.match(/\s(?:INFO|WARN|ERROR|DEBUG)\s(.+)$/)
  return match ? match[1] : line
}

const getLogLineClass = (line: string) => {
  const level = extractLevel(line)
  return {
    'log-info': level === 'INFO',
    'log-warn': level === 'WARN',
    'log-error': level === 'ERROR',
    'log-debug': level === 'DEBUG'
  }
}

const startLogRefresh = () => {
  if (currentInstance.value?.status === 'RUNNING') {
    logRefreshInterval.value = window.setInterval(() => {
      loadLogs()
    }, 5000) // 每5秒刷新一次日志
  }
}

const stopLogRefresh = () => {
  if (logRefreshInterval.value) {
    clearInterval(logRefreshInterval.value)
    logRefreshInterval.value = undefined
  }
}

// 监听器
watch(() => props.visible, (visible) => {
  if (visible && props.task) {
    loadInstances()
  } else {
    stopLogRefresh()
  }
})

watch(currentInstance, (instance) => {
  stopLogRefresh()
  if (instance?.status === 'RUNNING') {
    startLogRefresh()
  }
})

watch(autoScroll, (enabled) => {
  if (enabled) {
    scrollToBottom()
  }
})

// 生命周期
onUnmounted(() => {
  stopLogRefresh()
})
</script>

<style scoped>
/* 模态框样式 */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.header-info {
  @apply flex-1;
}

.header-info h2 {
  @apply text-xl font-semibold text-gray-900 m-0 mb-2;
}

.task-info {
  @apply flex items-center gap-3;
}

.task-name {
  @apply text-sm text-gray-600 font-medium;
}

.close-button {
  @apply p-2 text-gray-400 hover:text-gray-600 transition-colors;
}

.close-button svg {
  @apply w-5 h-5;
}

.modal-body {
  @apply p-6 overflow-y-auto max-h-[calc(90vh-120px)] space-y-6;
}

/* 实例选择器 */
.instance-selector {
  @apply bg-gray-50 rounded-lg p-4;
}

.selector-header {
  @apply flex items-center justify-between mb-4;
}

.selector-header h3 {
  @apply text-lg font-medium text-gray-900 m-0;
}

.selector-actions {
  @apply flex gap-2;
}

.instances-list {
  @apply space-y-2 max-h-40 overflow-y-auto;
}

.instance-item {
  @apply flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200 cursor-pointer hover:border-blue-300 transition-colors;
}

.instance-item.active {
  @apply border-blue-500 bg-blue-50;
}

.instance-info {
  @apply flex-1;
}

.instance-id {
  @apply font-medium text-gray-900;
}

.instance-time {
  @apply text-sm text-gray-600 mt-1;
}

.instance-status {
  @apply mx-4;
}

.instance-duration {
  @apply text-sm text-gray-500 font-mono;
}

/* 日志查看器 */
.logs-section {
  @apply bg-white border border-gray-200 rounded-lg overflow-hidden;
}

.logs-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50;
}

.logs-header h3 {
  @apply text-lg font-medium text-gray-900 m-0;
}

.logs-actions {
  @apply flex items-center gap-4;
}

.auto-scroll-toggle {
  @apply flex items-center gap-2 cursor-pointer;
}

.auto-scroll-toggle input[type="checkbox"] {
  @apply w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500;
}

.auto-scroll-toggle span {
  @apply text-sm text-gray-700;
}

.logs-viewer {
  @apply h-96 overflow-y-auto bg-gray-900 text-gray-100 font-mono text-sm;
}

.logs-loading {
  @apply flex items-center justify-center h-full text-gray-400;
}

.logs-loading svg {
  @apply w-5 h-5 mr-2;
}

.logs-empty {
  @apply flex items-center justify-center h-full text-gray-400;
}

.logs-content {
  @apply p-4 space-y-1;
}

.log-line {
  @apply flex items-start gap-3 py-1 hover:bg-gray-800 transition-colors;
}

.log-timestamp {
  @apply text-gray-400 text-xs flex-shrink-0 w-20;
}

.log-level {
  @apply text-xs font-bold flex-shrink-0 w-12;
}

.log-message {
  @apply flex-1 break-words;
}

.log-info .log-level {
  @apply text-blue-400;
}

.log-warn .log-level {
  @apply text-yellow-400;
}

.log-error .log-level {
  @apply text-red-400;
}

.log-debug .log-level {
  @apply text-gray-400;
}

/* 执行控制 */
.execution-controls {
  @apply flex justify-center pt-4 border-t border-gray-200;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-sm {
  @apply px-3 py-1.5 text-sm;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.btn-icon {
  @apply w-4 h-4;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
