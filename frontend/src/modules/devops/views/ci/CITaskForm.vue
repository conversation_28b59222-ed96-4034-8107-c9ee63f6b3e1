<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h2>{{ isEditing ? '编辑 CI 任务' : '创建 CI 任务' }}</h2>
        <button @click="$emit('cancel')" class="close-button">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <form @submit.prevent="handleSubmit" class="task-form">
          <!-- 基础信息 -->
          <div class="form-section">
            <h3>基础信息</h3>
            <div class="form-grid">
              <FormField
                v-model="formData.name"
                label="任务名称"
                type="text"
                placeholder="请输入任务名称"
                required
                :error-message="formErrors.name"
              />

              <FormField
                v-model="formData.description"
                label="任务描述"
                type="textarea"
                placeholder="请输入任务描述"
                :rows="3"
                :error-message="formErrors.description"
              />

              <FormField
                v-model="formData.componentId"
                label="关联组件"
                type="select"
                :options="componentOptions"
                required
                :error-message="formErrors.componentId"
              />

              <FormField
                v-model="formData.taskType"
                label="任务类型"
                type="select"
                :options="taskTypeOptions"
                required
                :error-message="formErrors.taskType"
              />
            </div>
          </div>

          <!-- 触发配置 -->
          <div class="form-section">
            <h3>触发配置</h3>
            <div class="form-grid">
              <FormField
                v-model="formData.triggerType"
                label="触发类型"
                type="select"
                :options="triggerTypeOptions"
                required
                :error-message="formErrors.triggerType"
                @change="handleTriggerTypeChange"
              />

              <FormField
                v-if="formData.triggerType === 'schedule'"
                v-model="formData.schedule"
                label="定时表达式"
                type="text"
                placeholder="0 0 2 * * ?"
                :error-message="formErrors.schedule"
                help-text="使用 Cron 表达式格式"
              />

              <FormField
                v-model="formData.timeout"
                label="超时时间（分钟）"
                type="number"
                :min="1"
                :max="1440"
                :error-message="formErrors.timeout"
              />
            </div>
          </div>

          <!-- 执行配置 -->
          <div class="form-section">
            <h3>执行配置</h3>
            <div class="config-tabs">
              <button
                v-for="tab in configTabs"
                :key="tab.key"
                type="button"
                :class="['tab-button', { active: activeConfigTab === tab.key }]"
                @click="activeConfigTab = tab.key"
              >
                {{ tab.label }}
              </button>
            </div>

            <div class="config-content">
              <!-- 基础配置 -->
              <div v-if="activeConfigTab === 'basic'" class="config-panel">
                <div class="form-grid">
                  <FormField
                    v-model="formData.configuration.workingDirectory"
                    label="工作目录"
                    type="text"
                    placeholder="/workspace"
                  />

                  <FormField
                    v-model="formData.configuration.environment"
                    label="环境变量"
                    type="textarea"
                    placeholder="KEY1=value1&#10;KEY2=value2"
                    :rows="4"
                    help-text="每行一个环境变量，格式：KEY=VALUE"
                  />
                </div>
              </div>

              <!-- 脚本配置 -->
              <div v-if="activeConfigTab === 'script'" class="config-panel">
                <div class="script-editor-section">
                  <YamlEditor
                    v-model="formData.configuration.script"
                    title="执行脚本配置"
                    description="配置 CI 任务的执行脚本，支持 YAML 格式"
                    height="400px"
                    :templates="scriptTemplates"
                    @validate="handleScriptValidation"
                  />
                </div>
              </div>

              <!-- 通知配置 -->
              <div v-if="activeConfigTab === 'notification'" class="config-panel">
                <div class="form-grid">
                  <div class="checkbox-group">
                    <label class="checkbox-item">
                      <input
                        v-model="formData.configuration.notifyOnSuccess"
                        type="checkbox"
                      />
                      <span>成功时通知</span>
                    </label>
                    <label class="checkbox-item">
                      <input
                        v-model="formData.configuration.notifyOnFailure"
                        type="checkbox"
                      />
                      <span>失败时通知</span>
                    </label>
                  </div>

                  <FormField
                    v-model="formData.configuration.notificationChannels"
                    label="通知渠道"
                    type="select"
                    multiple
                    :options="notificationChannelOptions"
                    help-text="可选择多个通知渠道"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 表单操作 -->
          <div class="form-actions">
            <button
              type="button"
              @click="validateConfiguration"
              :disabled="validatingConfig"
              class="btn btn-secondary"
            >
              <svg v-if="validatingConfig" class="btn-icon animate-spin" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
              </svg>
              <svg v-else class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              {{ validatingConfig ? '验证中...' : '验证配置' }}
            </button>

            <div class="action-buttons">
              <button
                type="button"
                @click="$emit('cancel')"
                class="btn btn-secondary"
              >
                取消
              </button>
              <button
                type="submit"
                :disabled="submitting"
                class="btn btn-primary"
              >
                <svg v-if="submitting" class="btn-icon animate-spin" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
                {{ submitting ? '保存中...' : (isEditing ? '更新任务' : '创建任务') }}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import FormField from '@/components/FormField.vue'
import YamlEditor from '@/components/YamlEditor.vue'
import { YamlTemplate, ValidationError } from '@/components/types/YamlEditor'
import type { DevOpsCiTask, DevOpsComponent } from '@/modules/devops/types/devops'

// Props
interface Props {
  visible: boolean
  task?: DevOpsCiTask | null
  components: DevOpsComponent[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  submit: [data: any]
  cancel: []
}>()

// 响应式数据
const submitting = ref(false)
const validatingConfig = ref(false)
const activeConfigTab = ref('basic')

const formData = ref({
  name: '',
  description: '',
  componentId: null as number | null,
  taskType: 'build',
  triggerType: 'manual',
  schedule: '',
  timeout: 30,
  configuration: {
    workingDirectory: '/workspace',
    environment: '',
    script: '',
    notifyOnSuccess: false,
    notifyOnFailure: true,
    notificationChannels: []
  }
})

const formErrors = ref({
  name: '',
  description: '',
  componentId: '',
  taskType: '',
  triggerType: '',
  schedule: '',
  timeout: ''
})

// 计算属性
const isEditing = computed(() => !!props.task)

const componentOptions = computed(() =>
  props.components.map(component => ({
    value: component.id,
    label: component.name
  }))
)

const taskTypeOptions = [
  { value: 'build', label: '构建' },
  { value: 'test', label: '测试' },
  { value: 'lint', label: '代码检查' },
  { value: 'security_scan', label: '安全扫描' },
  { value: 'custom', label: '自定义' }
]

const triggerTypeOptions = [
  { value: 'manual', label: '手动触发' },
  { value: 'schedule', label: '定时触发' },
  { value: 'webhook', label: 'Webhook 触发' },
  { value: 'push', label: '代码推送触发' },
  { value: 'pull_request', label: 'Pull Request 触发' }
]

const notificationChannelOptions = [
  { value: 'email', label: '邮件' },
  { value: 'slack', label: 'Slack' },
  { value: 'webhook', label: 'Webhook' },
  { value: 'dingtalk', label: '钉钉' }
]

const configTabs = [
  { key: 'basic', label: '基础配置' },
  { key: 'script', label: '脚本配置' },
  { key: 'notification', label: '通知配置' }
]

const scriptTemplates = computed(() => [
  {
    id: 'ci-build',
    name: 'CI 构建脚本',
    description: '标准的持续集成构建流水线',
    content: `name: CI Build Pipeline
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linting
      run: npm run lint

    - name: Run tests
      run: npm test -- --coverage

    - name: Build application
      run: npm run build`
  }
] as YamlTemplate[])

// 方法
const handleOverlayClick = () => {
  emit('cancel')
}

const handleTriggerTypeChange = () => {
  if (formData.value.triggerType !== 'schedule') {
    formData.value.schedule = ''
    formErrors.value.schedule = ''
  }
}

const handleScriptValidation = (errors: ValidationError[]) => {
  if (errors && errors.length > 0) {
    console.warn('脚本验证失败:', errors)
  }
}

const validateForm = () => {
  const errors = {
    name: '',
    description: '',
    componentId: '',
    taskType: '',
    triggerType: '',
    schedule: '',
    timeout: ''
  }
  let isValid = true

  if (!formData.value.name.trim()) {
    errors.name = '任务名称不能为空'
    isValid = false
  }

  if (!formData.value.componentId) {
    errors.componentId = '请选择关联组件'
    isValid = false
  }

  if (!formData.value.taskType) {
    errors.taskType = '请选择任务类型'
    isValid = false
  }

  if (formData.value.triggerType === 'schedule' && !formData.value.schedule) {
    errors.schedule = '定时触发需要配置定时表达式'
    isValid = false
  }

  if (formData.value.timeout < 1 || formData.value.timeout > 1440) {
    errors.timeout = '超时时间必须在 1-1440 分钟之间'
    isValid = false
  }

  formErrors.value = errors
  return isValid
}

const validateConfiguration = async () => {
  validatingConfig.value = true
  try {
    // 模拟配置验证
    await new Promise(resolve => setTimeout(resolve, 2000))

    if (!formData.value.configuration.script) {
      alert('请配置执行脚本')
      return
    }

    alert('配置验证通过！')
  } catch (error) {
    alert(`配置验证失败: ${error}`)
  } finally {
    validatingConfig.value = false
  }
}

const handleSubmit = async () => {
  if (!validateForm()) return

  submitting.value = true
  try {
    emit('submit', { ...formData.value })
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    componentId: null,
    taskType: 'build',
    triggerType: 'manual',
    schedule: '',
    timeout: 30,
    configuration: {
      workingDirectory: '/workspace',
      environment: '',
      script: '',
      notifyOnSuccess: false,
      notifyOnFailure: true,
      notificationChannels: []
    }
  }
  formErrors.value = {
    name: '',
    description: '',
    componentId: '',
    taskType: '',
    triggerType: '',
    schedule: '',
    timeout: ''
  }
}

// 监听器
watch(() => props.visible, (visible) => {
  if (visible) {
    if (props.task) {
      // 编辑模式，填充表单数据
      formData.value = {
        name: props.task.name,
        description: props.task.description || '',
        componentId: props.task.componentId,
        taskType: props.task.taskType || 'build',
        triggerType: props.task.triggerType || 'manual',
        schedule: props.task.configuration.schedule || '',
        timeout: props.task.timeout || 30,
        configuration: {
          workingDirectory: props.task.configuration?.workingDirectory || '/workspace',
          environment: props.task.configuration?.environment || '',
          script: props.task.configuration?.script || '',
          notifyOnSuccess: props.task.configuration?.notifyOnSuccess || false,
          notifyOnFailure: props.task.configuration?.notifyOnFailure || true,
          notificationChannels: props.task.configuration?.notificationChannels || []
        }
      }
    } else {
      // 创建模式，重置表单
      resetForm()
    }
  }
})
</script>

<style scoped>
/* 模态框样式 */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.modal-header h2 {
  @apply text-xl font-semibold text-gray-900 m-0;
}

.close-button {
  @apply p-2 text-gray-400 hover:text-gray-600 transition-colors;
}

.close-button svg {
  @apply w-5 h-5;
}

.modal-body {
  @apply p-6 overflow-y-auto max-h-[calc(90vh-120px)];
}

/* 表单样式 */
.task-form {
  @apply space-y-8;
}

.form-section {
  @apply space-y-4;
}

.form-section h3 {
  @apply text-lg font-medium text-gray-900 m-0 pb-2 border-b border-gray-200;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

/* 配置标签页 */
.config-tabs {
  @apply flex border-b border-gray-200 mb-6;
}

.tab-button {
  @apply px-4 py-2 text-sm font-medium text-gray-600 border-b-2 border-transparent hover:text-gray-900 hover:border-gray-300 transition-colors;
}

.tab-button.active {
  @apply text-blue-600 border-blue-600;
}

.config-content {
  @apply min-h-[300px];
}

.config-panel {
  @apply space-y-6;
}

/* 脚本编辑器 */
.script-editor-section {
  @apply space-y-4;
}

/* 复选框组 */
.checkbox-group {
  @apply space-y-3;
}

.checkbox-item {
  @apply flex items-center gap-3 cursor-pointer;
}

.checkbox-item input[type="checkbox"] {
  @apply w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500;
}

.checkbox-item span {
  @apply text-sm text-gray-700;
}

/* 表单操作 */
.form-actions {
  @apply flex items-center justify-between pt-6 border-t border-gray-200;
}

.action-buttons {
  @apply flex items-center gap-3;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.btn-icon {
  @apply w-4 h-4;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
