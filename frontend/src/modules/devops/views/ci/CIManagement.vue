<template>
  <div class="ci-management">
    <!-- 面包屑导航 -->
    <Breadcrumb :items="breadcrumbItems" />

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h1>CI 管理</h1>
          <p>管理持续集成任务，配置构建、测试和代码质量检查流程</p>
        </div>
        <div class="header-actions">
          <button
            @click="showCreateModal = true"
            class="btn btn-primary"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            创建 CI 任务
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card total-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm6 5a1 1 0 10-2 0v6a1 1 0 102 0V7z" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.total }}</div>
          <div class="stat-label">总任务数</div>
        </div>
      </div>

      <div class="stat-card running-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.running }}</div>
          <div class="stat-label">运行中</div>
        </div>
      </div>

      <div class="stat-card completed-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.completed }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>

      <div class="stat-card failed-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.failed }}</div>
          <div class="stat-label">失败</div>
        </div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters-section">
      <div class="filter-group">
        <label class="filter-label">按组件筛选：</label>
        <select
          v-model="selectedComponentId"
          @change="handleComponentFilter"
          class="filter-select"
        >
          <option value="">全部组件</option>
          <option
            v-for="component in components"
            :key="component.id"
            :value="component.id"
          >
            {{ component.name }}
          </option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">任务类型：</label>
        <select
          v-model="selectedTaskType"
          @change="handleTaskTypeFilter"
          class="filter-select"
        >
          <option value="">全部类型</option>
          <option value="build">构建</option>
          <option value="test">测试</option>
          <option value="lint">代码检查</option>
          <option value="security_scan">安全扫描</option>
          <option value="custom">自定义</option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">状态筛选：</label>
        <select
          v-model="selectedStatus"
          @change="handleStatusFilter"
          class="filter-select"
        >
          <option value="">全部状态</option>
          <option value="ACTIVE">活跃</option>
          <option value="INACTIVE">非活跃</option>
          <option value="RUNNING">运行中</option>
          <option value="COMPLETED">已完成</option>
          <option value="FAILED">失败</option>
        </select>
      </div>
    </div>

    <!-- CI 任务列表 -->
    <CITaskList
      :tasks="filteredTasks"
      :loading="loading"
      :components="components"
      @create="handleCreateTask"
      @edit="handleEditTask"
      @delete="handleDeleteTask"
      @execute="handleExecuteTask"
      @view-logs="handleViewLogs"
      @refresh="refreshTasks"
    />

    <!-- 创建/编辑任务模态框 -->
    <CITaskForm
      v-if="showCreateModal || showEditModal"
      :visible="showCreateModal || showEditModal"
      :task="editingTask"
      :components="components"
      @submit="handleSubmitTask"
      @cancel="handleCancelTask"
    />

    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-if="showDeleteDialog"
      :visible="showDeleteDialog"
      title="删除 CI 任务"
      :message="`确定要删除任务 '${deleteTarget?.name}' 吗？此操作不可撤销。`"
      confirm-text="删除"
      cancel-text="取消"
      type="danger"
      :loading="deleting"
      @confirm="confirmDelete"
      @cancel="showDeleteDialog = false"
    />

    <!-- 日志查看模态框 -->
    <CIExecutionLogs
      v-if="showLogsModal"
      :visible="showLogsModal"
      :task="currentTask"
      @close="showLogsModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import Breadcrumb from '@/components/Breadcrumb.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import CITaskList from './CITaskList.vue'
import CITaskForm from './CITaskForm.vue'
import CIExecutionLogs from './CIExecutionLogs.vue'
import { useCiStore } from '@/modules/devops/stores/ci'
import { useDevOpsStore } from '@/modules/devops/stores/devops'
import type { DevOpsCiTask, DevOpsComponent, BreadcrumbItem } from '@/modules/devops/types/devops'

const router = useRouter()
const ciStore = useCiStore()
const devopsStore = useDevOpsStore()

// 响应式数据
const loading = ref(false)
const deleting = ref(false)
const selectedComponentId = ref<number | ''>('')
const selectedTaskType = ref('')
const selectedStatus = ref('')

// 模态框状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteDialog = ref(false)
const showLogsModal = ref(false)
const editingTask = ref<DevOpsCiTask | null>(null)
const deleteTarget = ref<DevOpsCiTask | null>(null)
const currentTask = ref<DevOpsCiTask | null>(null)

// 计算属性
const breadcrumbItems = computed<BreadcrumbItem[]>(() => [
  { title: 'DevOps 管理', path: '/devops', icon: 'home' },
  { title: 'CI/CD 管理', path: '/devops/cicd', icon: 'cicd' },
  { title: 'CI 管理', active: true, icon: 'ci' }
])

const tasks = computed(() => ciStore.tasks)
const components = computed(() => devopsStore.components)

const stats = computed(() => ({
  total: tasks.value.length,
  running: tasks.value.filter(task => task.status === 'RUNNING').length,
  completed: tasks.value.filter(task => task.status === 'COMPLETED').length,
  failed: tasks.value.filter(task => task.status === 'FAILED').length
}))

const filteredTasks = computed(() => {
  let result = tasks.value

  // 按组件筛选
  if (selectedComponentId.value) {
    result = result.filter(task => task.componentId === selectedComponentId.value)
  }

  // 按任务类型筛选
  if (selectedTaskType.value) {
    result = result.filter(task => task.taskType === selectedTaskType.value)
  }

  // 按状态筛选
  if (selectedStatus.value) {
    result = result.filter(task => task.status === selectedStatus.value)
  }

  return result
})

// 方法
const loadTasks = async () => {
  loading.value = true
  try {
    await ciStore.fetchTasks()
  } finally {
    loading.value = false
  }
}

const loadComponents = async () => {
  try {
    await devopsStore.loadComponents()
  } catch (error) {
    console.error('加载组件失败:', error)
  }
}

const refreshTasks = () => {
  loadTasks()
}

const handleComponentFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleTaskTypeFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleStatusFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleCreateTask = () => {
  editingTask.value = null
  showCreateModal.value = true
}

const handleEditTask = (task: DevOpsCiTask) => {
  editingTask.value = task
  showEditModal.value = true
}

const handleDeleteTask = (task: DevOpsCiTask) => {
  deleteTarget.value = task
  showDeleteDialog.value = true
}

const handleExecuteTask = async (task: DevOpsCiTask) => {
  try {
    await ciStore.executeTask(task.id!, {})
    // 可以显示成功消息或跳转到执行日志页面
  } catch (error) {
    console.error('执行任务失败:', error)
  }
}

const handleViewLogs = (task: DevOpsCiTask) => {
  currentTask.value = task
  showLogsModal.value = true
}

const handleSubmitTask = async (taskData: any) => {
  try {
    if (editingTask.value) {
      await ciStore.updateTask(editingTask.value.id!, taskData)
    } else {
      await ciStore.createTask(taskData)
    }
    handleCancelTask()
  } catch (error) {
    console.error('保存任务失败:', error)
  }
}

const handleCancelTask = () => {
  showCreateModal.value = false
  showEditModal.value = false
  editingTask.value = null
}

const confirmDelete = async () => {
  if (!deleteTarget.value) return

  deleting.value = true
  try {
    await ciStore.deleteTask(deleteTarget.value.id!)
    showDeleteDialog.value = false
    deleteTarget.value = null
  } catch (error) {
    console.error('删除任务失败:', error)
  } finally {
    deleting.value = false
  }
}

// 生命周期
onMounted(() => {
  loadComponents()
  loadTasks()
})
</script>
