<template>
  <div class="cd-management">
    <!-- 面包屑导航 -->
    <Breadcrumb :items="breadcrumbItems" />

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h1>CD 管理</h1>
          <p>管理持续部署任务，配置自动化部署、回滚和环境管理流程</p>
        </div>
        <div class="header-actions">
          <button
            @click="showCreateModal = true"
            class="btn btn-primary"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            创建 CD 任务
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card total-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm1 5a1 1 0 000 2h4.586l-2.293 2.293a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 10-1.414 1.414L9.586 7H5z" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.total }}</div>
          <div class="stat-label">总任务数</div>
        </div>
      </div>

      <div class="stat-card deploying-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.deploying }}</div>
          <div class="stat-label">部署中</div>
        </div>
      </div>

      <div class="stat-card deployed-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.deployed }}</div>
          <div class="stat-label">已部署</div>
        </div>
      </div>

      <div class="stat-card failed-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.failed }}</div>
          <div class="stat-label">失败</div>
        </div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters-section">
      <div class="filter-group">
        <label class="filter-label">按应用筛选：</label>
        <select
          v-model="selectedApplicationId"
          @change="handleApplicationFilter"
          class="filter-select"
        >
          <option value="">全部应用</option>
          <option
            v-for="application in applications"
            :key="application.id"
            :value="application.id"
          >
            {{ application.name }}
          </option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">部署策略：</label>
        <select
          v-model="selectedDeploymentStrategy"
          @change="handleStrategyFilter"
          class="filter-select"
        >
          <option value="">全部策略</option>
          <option value="rolling_update">滚动更新</option>
          <option value="blue_green">蓝绿部署</option>
          <option value="canary">金丝雀部署</option>
          <option value="a_b_testing">A/B 测试</option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">目标环境：</label>
        <select
          v-model="selectedEnvironment"
          @change="handleEnvironmentFilter"
          class="filter-select"
        >
          <option value="">全部环境</option>
          <option value="development">开发环境</option>
          <option value="testing">测试环境</option>
          <option value="staging">预发布环境</option>
          <option value="production">生产环境</option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">状态筛选：</label>
        <select
          v-model="selectedStatus"
          @change="handleStatusFilter"
          class="filter-select"
        >
          <option value="">全部状态</option>
          <option value="ACTIVE">活跃</option>
          <option value="INACTIVE">非活跃</option>
          <option value="DEPLOYING">部署中</option>
          <option value="DEPLOYED">已部署</option>
          <option value="FAILED">失败</option>
        </select>
      </div>
    </div>

    <!-- CD 任务列表 -->
    <CDTaskList
      :tasks="filteredTasks"
      :loading="loading"
      :applications="applications"
      @create="handleCreateTask"
      @edit="handleEditTask"
      @delete="handleDeleteTask"
      @deploy="handleDeployTask"
      @rollback="handleRollbackTask"
      @view-logs="handleViewLogs"
      @refresh="refreshTasks"
    />

    <!-- 创建/编辑任务模态框 -->
    <CDTaskForm
      v-if="showCreateModal || showEditModal"
      :visible="showCreateModal || showEditModal"
      :task="editingTask"
      :applications="applications"
      @submit="handleSubmitTask"
      @cancel="handleCancelTask"
    />

    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-if="showDeleteDialog"
      :visible="showDeleteDialog"
      title="删除 CD 任务"
      :message="`确定要删除任务 '${deleteTarget?.name}' 吗？此操作不可撤销。`"
      confirm-text="删除"
      cancel-text="取消"
      type="danger"
      :loading="deleting"
      @confirm="confirmDelete"
      @cancel="showDeleteDialog = false"
    />

    <!-- 部署确认对话框 -->
    <ConfirmDialog
      v-if="showDeployDialog"
      :visible="showDeployDialog"
      title="确认部署"
      :message="`确定要部署任务 '${deployTarget?.name}' 到 '${deployTarget?.configuration?.targetEnvironment}' 环境吗？`"
      confirm-text="部署"
      cancel-text="取消"
      type="warning"
      :loading="deploying"
      @confirm="confirmDeploy"
      @cancel="showDeployDialog = false"
    />

    <!-- 回滚确认对话框 -->
    <ConfirmDialog
      v-if="showRollbackDialog"
      :visible="showRollbackDialog"
      title="确认回滚"
      :message="`确定要回滚任务 '${rollbackTarget?.name}' 到上一个版本吗？`"
      confirm-text="回滚"
      cancel-text="取消"
      type="warning"
      :loading="rollingBack"
      @confirm="confirmRollback"
      @cancel="showRollbackDialog = false"
    />

    <!-- 部署日志模态框 -->
    <CDDeploymentLogs
      v-if="showLogsModal"
      :visible="showLogsModal"
      :task="currentTask"
      @close="showLogsModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import Breadcrumb from '@/components/Breadcrumb.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import CDTaskList from './CDTaskList.vue'
import CDTaskForm from './CDTaskForm.vue'
import CDDeploymentLogs from './CDDeploymentLogs.vue'
import { useCdStore } from '@/modules/devops/stores/cd'
import { useDevOpsStore } from '@/modules/devops/stores/devops'
import type { DevOpsCdTask, DevOpsApplication, BreadcrumbItem } from '@/modules/devops/types/devops'

const router = useRouter()
const cdStore = useCdStore()
const devopsStore = useDevOpsStore()

// 响应式数据
const loading = ref(false)
const deleting = ref(false)
const deploying = ref(false)
const rollingBack = ref(false)
const selectedApplicationId = ref<number | ''>('')
const selectedDeploymentStrategy = ref('')
const selectedEnvironment = ref('')
const selectedStatus = ref('')

// 模态框状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteDialog = ref(false)
const showDeployDialog = ref(false)
const showRollbackDialog = ref(false)
const showLogsModal = ref(false)
const editingTask = ref<DevOpsCdTask | null>(null)
const deleteTarget = ref<DevOpsCdTask | null>(null)
const deployTarget = ref<DevOpsCdTask | null>(null)
const rollbackTarget = ref<DevOpsCdTask | null>(null)
const currentTask = ref<DevOpsCdTask | null>(null)

// 计算属性
const breadcrumbItems = computed<BreadcrumbItem[]>(() => [
  { title: 'DevOps 管理', path: '/devops', icon: 'home' },
  { title: 'CI/CD 管理', path: '/devops/cicd', icon: 'cicd' },
  { title: 'CD 管理', active: true, icon: 'cd' }
])

const tasks = computed(() => cdStore.tasks)
const applications = computed(() => devopsStore.applications)

const stats = computed(() => ({
  total: tasks.value.length,
  deploying: tasks.value.filter(task => task.status === 'DEPLOYING').length,
  deployed: tasks.value.filter(task => task.status === 'DEPLOYED').length,
  failed: tasks.value.filter(task => task.status === 'FAILED').length
}))

const filteredTasks = computed(() => {
  let result = tasks.value

  // 按应用筛选
  if (selectedApplicationId.value) {
    result = result.filter(task => task.applicationId === selectedApplicationId.value)
  }

  // 按部署策略筛选
  if (selectedDeploymentStrategy.value) {
    result = result.filter(task => task.deploymentStrategy === selectedDeploymentStrategy.value)
  }

  // 按目标环境筛选
  if (selectedEnvironment.value) {
    result = result.filter(task => task.configuration?.targetEnvironment === selectedEnvironment.value)
  }

  // 按状态筛选
  if (selectedStatus.value) {
    result = result.filter(task => task.status === selectedStatus.value)
  }

  return result
})

// 方法
const loadTasks = async () => {
  loading.value = true
  try {
    await cdStore.fetchTasks()
  } finally {
    loading.value = false
  }
}

const loadApplications = async () => {
  try {
    await devopsStore.loadApplications()
  } catch (error) {
    console.error('加载应用失败:', error)
  }
}

const refreshTasks = () => {
  loadTasks()
}

const handleApplicationFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleStrategyFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleEnvironmentFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleStatusFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleCreateTask = () => {
  editingTask.value = null
  showCreateModal.value = true
}

const handleEditTask = (task: DevOpsCdTask) => {
  editingTask.value = task
  showEditModal.value = true
}

const handleDeleteTask = (task: DevOpsCdTask) => {
  deleteTarget.value = task
  showDeleteDialog.value = true
}

const handleDeployTask = (task: DevOpsCdTask) => {
  deployTarget.value = task
  showDeployDialog.value = true
}

const handleRollbackTask = (task: DevOpsCdTask) => {
  rollbackTarget.value = task
  showRollbackDialog.value = true
}

const handleViewLogs = (task: DevOpsCdTask) => {
  currentTask.value = task
  showLogsModal.value = true
}

const handleSubmitTask = async (taskData: any) => {
  try {
    if (editingTask.value) {
      await cdStore.updateTask(editingTask.value.id!, taskData)
    } else {
      // 创建任务时需要传递applicationId
      await cdStore.createTask(taskData)
    }
    handleCancelTask()
  } catch (error) {
    console.error('保存任务失败:', error)
  }
}

const handleCancelTask = () => {
  showCreateModal.value = false
  showEditModal.value = false
  editingTask.value = null
}

const confirmDelete = async () => {
  if (!deleteTarget.value) return

  deleting.value = true
  try {
    await cdStore.deleteTask(deleteTarget.value.id!)
    showDeleteDialog.value = false
    deleteTarget.value = null
  } catch (error) {
    console.error('删除任务失败:', error)
  } finally {
    deleting.value = false
  }
}

const confirmDeploy = async () => {
  if (!deployTarget.value) return

  deploying.value = true
  try {
    await cdStore.deployTask(deployTarget.value.id!, {})
    showDeployDialog.value = false
    deployTarget.value = null
  } catch (error) {
    console.error('部署任务失败:', error)
  } finally {
    deploying.value = false
  }
}

const confirmRollback = async () => {
  if (!rollbackTarget.value) return

  rollingBack.value = true
  try {
    await cdStore.rollbackTask(rollbackTarget.value.id!)
    showRollbackDialog.value = false
    rollbackTarget.value = null
  } catch (error) {
    console.error('回滚任务失败:', error)
  } finally {
    rollingBack.value = false
  }
}

// 生命周期
onMounted(() => {
  loadApplications()
  loadTasks()
})
</script>

<style scoped>
.cd-management {
  @apply p-6 bg-gray-50 min-h-screen;
}

/* 页面头部 */
.page-header {
  @apply mb-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.header-title h1 {
  @apply text-2xl font-bold text-gray-900 m-0;
}

.header-title p {
  @apply text-gray-600 mt-1 m-0;
}

.header-actions {
  @apply flex gap-3;
}

/* 统计卡片 */
.stats-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8;
}

.stat-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow;
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mb-4;
}

.stat-icon svg {
  @apply w-6 h-6;
}

.total-card .stat-icon {
  @apply bg-green-100 text-green-600;
}

.deploying-card .stat-icon {
  @apply bg-blue-100 text-blue-600;
}

.deployed-card .stat-icon {
  @apply bg-green-100 text-green-600;
}

.failed-card .stat-icon {
  @apply bg-red-100 text-red-600;
}

.stat-number {
  @apply text-2xl font-bold text-gray-900 mb-1;
}

.stat-label {
  @apply text-sm font-medium text-gray-600;
}

/* 筛选器 */
.filters-section {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6;
}

.filters-section {
  @apply flex flex-wrap gap-6;
}

.filter-group {
  @apply flex items-center gap-2;
}

.filter-label {
  @apply text-sm font-medium text-gray-700 whitespace-nowrap;
}

.filter-select {
  @apply px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-icon {
  @apply w-4 h-4;
}
</style>
