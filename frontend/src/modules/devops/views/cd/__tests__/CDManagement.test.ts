/**
 * CDManagement组件的单元测试
 * 测试CD任务管理页面的所有功能
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import CDManagement from '../CDManagement.vue'
import { reactive } from 'vue'

// Mock组件
vi.mock('@/components/Breadcrumb.vue', () => ({
  default: {
    name: 'Breadcrumb',
    template: '<div data-testid="breadcrumb"><slot></slot></div>',
    props: ['items']
  }
}))

vi.mock('@/components/ConfirmDialog.vue', () => ({
  default: {
    name: 'ConfirmDialog',
    template: '<div data-testid="confirm-dialog" v-if="visible"></div>',
    props: ['visible', 'title', 'message', 'type', 'confirm-text', 'cancel-text', 'loading'],
    emits: ['confirm', 'cancel']
  }
}))

vi.mock('../CDTaskList.vue', () => ({
  default: {
    name: 'CDTaskList',
    template: '<div data-testid="cd-task-list"></div>',
    props: ['tasks', 'loading', 'applications'],
    emits: ['create', 'edit', 'delete', 'deploy', 'rollback', 'view-logs', 'refresh']
  }
}))

vi.mock('../CDTaskForm.vue', () => ({
  default: {
    name: 'CDTaskForm',
    template: '<div data-testid="cd-task-form" v-if="visible"></div>',
    props: ['visible', 'task', 'applications'],
    emits: ['submit', 'cancel']
  }
}))

vi.mock('../CDDeploymentLogs.vue', () => ({
  default: {
    name: 'CDDeploymentLogs',
    template: '<div data-testid="cd-deployment-logs" v-if="visible"></div>',
    props: ['visible', 'task'],
    emits: ['close']
  }
}))

// Mock stores
const mockCdStore = reactive({
  tasks: [],
  loading: false,
  error: null,
  fetchTasks: vi.fn(),
  createTask: vi.fn(),
  updateTask: vi.fn(),
  deleteTask: vi.fn(),
  deployTask: vi.fn(),
  rollbackTask: vi.fn(),
  clearError: vi.fn()
})

const mockDevOpsStore = reactive({
  applications: [],
  loading: { applications: false },
  loadApplications: vi.fn()
})

vi.mock('@/modules/devops/stores/cd', () => ({
  useCdStore: () => mockCdStore
}))

vi.mock('@/modules/devops/stores/devops', () => ({
  useDevOpsStore: () => mockDevOpsStore
}))

// 创建路由
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/devops', component: { template: '<div>DevOps</div>' } },
    { path: '/devops/cicd', component: { template: '<div>CICD</div>' } }
  ]
})

describe('CDManagement', () => {
  let wrapper: any
  let pinia: any

  beforeEach(() => {
    pinia = createPinia()
    
    // 重置mock数据
    mockCdStore.tasks = [
      {
        id: 1,
        name: '前端部署任务',
        description: '前端应用部署',
        applicationId: 1,
        deploymentStrategy: 'rolling_update',
        status: 'ACTIVE',
        configuration: { targetEnvironment: 'production' },
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        name: '后端部署任务',
        description: '后端API部署',
        applicationId: 2,
        deploymentStrategy: 'blue_green',
        status: 'DEPLOYING',
        configuration: { targetEnvironment: 'staging' },
        createdAt: '2024-01-02T00:00:00Z'
      },
      {
        id: 3,
        name: '失败部署任务',
        description: '失败的部署',
        applicationId: 1,
        deploymentStrategy: 'canary',
        status: 'FAILED',
        configuration: { targetEnvironment: 'production' },
        createdAt: '2024-01-03T00:00:00Z'
      }
    ]
    
    mockDevOpsStore.applications = [
      { id: 1, name: '前端应用', description: '前端Web应用' },
      { id: 2, name: '后端应用', description: '后端API应用' }
    ]

    // 重置mock函数
    vi.clearAllMocks()
    
    wrapper = mount(CDManagement, {
      global: {
        plugins: [router, pinia]
      }
    })
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  describe('组件渲染', () => {
    it('正确渲染基本结构', () => {
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('[data-testid="breadcrumb"]').exists()).toBe(true)
      expect(wrapper.find('.page-header').exists()).toBe(true)
      expect(wrapper.find('.stats-grid').exists()).toBe(true)
      expect(wrapper.find('.filters-section').exists()).toBe(true)
      expect(wrapper.find('[data-testid="cd-task-list"]').exists()).toBe(true)
    })

    it('显示正确的页面标题和描述', () => {
      const header = wrapper.find('.page-header')
      expect(header.text()).toContain('CD 管理')
      expect(header.text()).toContain('管理持续部署任务，配置自动化部署、回滚和环境管理流程')
    })

    it('显示创建任务按钮', () => {
      const createButton = wrapper.find('.btn-primary')
      expect(createButton.exists()).toBe(true)
      expect(createButton.text()).toContain('创建 CD 任务')
    })
  })

  describe('统计卡片', () => {
    it('正确显示任务统计信息', () => {
      const statCards = wrapper.findAll('.stat-card')
      expect(statCards).toHaveLength(4)

      // 总任务数
      const totalCard = wrapper.find('.total-card')
      expect(totalCard.find('.stat-number').text()).toBe('3')
      expect(totalCard.find('.stat-label').text()).toBe('总任务数')

      // 部署中任务数
      const deployingCard = wrapper.find('.deploying-card')
      expect(deployingCard.find('.stat-number').text()).toBe('1')
      expect(deployingCard.find('.stat-label').text()).toBe('部署中')

      // 已部署任务数
      const deployedCard = wrapper.find('.deployed-card')
      expect(deployedCard.find('.stat-number').text()).toBe('0')
      expect(deployedCard.find('.stat-label').text()).toBe('已部署')

      // 失败任务数
      const failedCard = wrapper.find('.failed-card')
      expect(failedCard.find('.stat-number').text()).toBe('1')
      expect(failedCard.find('.stat-label').text()).toBe('失败')
    })

    it('当任务列表为空时显示零统计', async () => {
      mockCdStore.tasks = []
      await wrapper.vm.$nextTick()

      const statNumbers = wrapper.findAll('.stat-number')
      statNumbers.forEach(statNumber => {
        expect(statNumber.text()).toBe('0')
      })
    })
  })

  describe('筛选功能', () => {
    it('显示所有筛选器', () => {
      const filterGroups = wrapper.findAll('.filter-group')
      expect(filterGroups).toHaveLength(4)

      // 应用筛选器
      const applicationSelect = wrapper.find('select').element as HTMLSelectElement
      expect(applicationSelect.options).toHaveLength(3) // 全部应用 + 2个应用

      // 部署策略筛选器
      const strategySelects = wrapper.findAll('select')
      expect(strategySelects[1].element.options).toHaveLength(5) // 全部策略 + 4种策略

      // 目标环境筛选器
      expect(strategySelects[2].element.options).toHaveLength(5) // 全部环境 + 4种环境

      // 状态筛选器
      expect(strategySelects[3].element.options).toHaveLength(6) // 全部状态 + 5种状态
    })

    it('按应用筛选任务', async () => {
      const applicationSelect = wrapper.find('select')
      await applicationSelect.setValue('1')
      
      expect(wrapper.vm.selectedApplicationId).toBe(1)
      expect(wrapper.vm.filteredTasks).toHaveLength(2) // 应用1有2个任务
    })

    it('按部署策略筛选任务', async () => {
      const strategySelect = wrapper.findAll('select')[1]
      await strategySelect.setValue('rolling_update')
      
      expect(wrapper.vm.selectedDeploymentStrategy).toBe('rolling_update')
      expect(wrapper.vm.filteredTasks).toHaveLength(1) // rolling_update策略有1个任务
    })

    it('按目标环境筛选任务', async () => {
      const environmentSelect = wrapper.findAll('select')[2]
      await environmentSelect.setValue('production')
      
      expect(wrapper.vm.selectedEnvironment).toBe('production')
      expect(wrapper.vm.filteredTasks).toHaveLength(2) // production环境有2个任务
    })

    it('按状态筛选任务', async () => {
      const statusSelect = wrapper.findAll('select')[3]
      await statusSelect.setValue('DEPLOYING')
      
      expect(wrapper.vm.selectedStatus).toBe('DEPLOYING')
      expect(wrapper.vm.filteredTasks).toHaveLength(1) // DEPLOYING状态有1个任务
    })

    it('组合筛选条件', async () => {
      const selects = wrapper.findAll('select')
      await selects[0].setValue('1') // 应用1
      await selects[2].setValue('production') // 生产环境

      expect(wrapper.vm.filteredTasks).toHaveLength(2) // 应用1的生产环境任务有1个
    })
  })

  describe('模态框管理', () => {
    it('点击创建按钮显示创建模态框', async () => {
      const createButton = wrapper.find('.btn-primary')
      await createButton.trigger('click')

      expect(wrapper.vm.showCreateModal).toBe(true)
      expect(wrapper.vm.editingTask).toBeNull()
      expect(wrapper.find('[data-testid="cd-task-form"]').exists()).toBe(true)
    })

    it('处理编辑任务事件', async () => {
      const testTask = mockCdStore.tasks[0]
      const taskList = wrapper.findComponent({ name: 'CDTaskList' })

      await taskList.vm.$emit('edit', testTask)

      expect(wrapper.vm.showEditModal).toBe(true)
      expect(wrapper.vm.editingTask).toEqual(testTask)
      expect(wrapper.find('[data-testid="cd-task-form"]').exists()).toBe(true)
    })

    it('处理删除任务事件', async () => {
      const testTask = mockCdStore.tasks[0]
      const taskList = wrapper.findComponent({ name: 'CDTaskList' })

      await taskList.vm.$emit('delete', testTask)

      expect(wrapper.vm.showDeleteDialog).toBe(true)
      expect(wrapper.vm.deleteTarget).toEqual(testTask)
      expect(wrapper.find('[data-testid="confirm-dialog"]').exists()).toBe(true)
    })

    it('处理部署任务事件', async () => {
      const testTask = mockCdStore.tasks[0]
      const taskList = wrapper.findComponent({ name: 'CDTaskList' })

      await taskList.vm.$emit('deploy', testTask)

      expect(wrapper.vm.showDeployDialog).toBe(true)
      expect(wrapper.vm.deployTarget).toEqual(testTask)
    })

    it('处理回滚任务事件', async () => {
      const testTask = mockCdStore.tasks[0]
      const taskList = wrapper.findComponent({ name: 'CDTaskList' })

      await taskList.vm.$emit('rollback', testTask)

      expect(wrapper.vm.showRollbackDialog).toBe(true)
      expect(wrapper.vm.rollbackTarget).toEqual(testTask)
    })

    it('处理查看日志事件', async () => {
      const testTask = mockCdStore.tasks[0]
      const taskList = wrapper.findComponent({ name: 'CDTaskList' })

      await taskList.vm.$emit('view-logs', testTask)

      expect(wrapper.vm.showLogsModal).toBe(true)
      expect(wrapper.vm.currentTask).toEqual(testTask)
      expect(wrapper.find('[data-testid="cd-deployment-logs"]').exists()).toBe(true)
    })

    it('取消任务表单', async () => {
      wrapper.vm.showCreateModal = true
      wrapper.vm.editingTask = mockCdStore.tasks[0]
      // 等待DOM更新
      await wrapper.vm.$nextTick()

      const taskForm = wrapper.findComponent({ name: 'CDTaskForm' })
      await taskForm.vm.$emit('cancel')

      expect(wrapper.vm.showCreateModal).toBe(false)
      expect(wrapper.vm.showEditModal).toBe(false)
      expect(wrapper.vm.editingTask).toBeNull()
    })
  })

  describe('任务操作', () => {
    it('创建新任务', async () => {
      const taskData = {
        name: '新部署任务',
        description: '新部署任务描述',
        applicationId: 1,
        deploymentStrategy: 'rolling_update'
      }

      wrapper.vm.showCreateModal = true
      // 等待DOM更新
      await wrapper.vm.$nextTick()
      const taskForm = wrapper.findComponent({ name: 'CDTaskForm' })
      await taskForm.vm.$emit('submit', taskData)

      expect(mockCdStore.createTask).toHaveBeenCalledWith(taskData)
      expect(wrapper.vm.showCreateModal).toBe(false)
    })

    it('更新现有任务', async () => {
      const testTask = mockCdStore.tasks[0]
      const updatedData = {
        name: '更新的部署任务',
        description: '更新的描述'
      }

      wrapper.vm.editingTask = testTask
      wrapper.vm.showEditModal = true
      // 等待DOM更新
      await wrapper.vm.$nextTick()
      const taskForm = wrapper.findComponent({ name: 'CDTaskForm' })
      await taskForm.vm.$emit('submit', updatedData)

      expect(mockCdStore.updateTask).toHaveBeenCalledWith(testTask.id, updatedData)
      expect(wrapper.vm.showEditModal).toBe(false)
    })

    it('确认删除任务', async () => {
      const testTask = mockCdStore.tasks[0]
      wrapper.vm.deleteTarget = testTask
      wrapper.vm.showDeleteDialog = true
      // 等待DOM更新
      await wrapper.vm.$nextTick()

      const confirmDialog = wrapper.findComponent({ name: 'ConfirmDialog' })
      await confirmDialog.vm.$emit('confirm')

      expect(mockCdStore.deleteTask).toHaveBeenCalledWith(testTask.id)
      expect(wrapper.vm.showDeleteDialog).toBe(false)
      expect(wrapper.vm.deleteTarget).toBeNull()
    })

    it('确认部署任务', async () => {
      const testTask = mockCdStore.tasks[0]
      wrapper.vm.deployTarget = testTask
      wrapper.vm.showDeployDialog = true
      // 等待DOM更新
      await wrapper.vm.$nextTick()

      const confirmDialog = wrapper.findComponent({ name: 'ConfirmDialog' })
      await confirmDialog.vm.$emit('confirm')

      expect(mockCdStore.deployTask).toHaveBeenCalledWith(testTask.id, {})
      expect(wrapper.vm.showDeployDialog).toBe(false)
      expect(wrapper.vm.deployTarget).toBeNull()
    })

    it('确认回滚任务', async () => {
      const testTask = mockCdStore.tasks[0]
      wrapper.vm.rollbackTarget = testTask
      wrapper.vm.showRollbackDialog = true
      // 等待DOM更新
      await wrapper.vm.$nextTick()

      const confirmDialog = wrapper.findComponent({ name: 'ConfirmDialog' })
      await confirmDialog.vm.$emit('confirm')

      expect(mockCdStore.rollbackTask).toHaveBeenCalledWith(testTask.id)
      expect(wrapper.vm.showRollbackDialog).toBe(false)
      expect(wrapper.vm.rollbackTarget).toBeNull()
    })

    it('取消删除任务', async () => {
      wrapper.vm.showDeleteDialog = true
      wrapper.vm.deleteTarget = mockCdStore.tasks[0]
      // 等待DOM更新
      await wrapper.vm.$nextTick()

      const confirmDialog = wrapper.findComponent({ name: 'ConfirmDialog' })
      await confirmDialog.vm.$emit('cancel')

      expect(wrapper.vm.showDeleteDialog).toBe(false)
      expect(mockCdStore.deleteTask).not.toHaveBeenCalled()
    })
  })

  describe('数据加载', () => {
    it('组件挂载时加载数据', () => {
      expect(mockCdStore.fetchTasks).toHaveBeenCalled()
      expect(mockDevOpsStore.loadApplications).toHaveBeenCalled()
    })

    it('刷新任务数据', async () => {
      vi.clearAllMocks()

      const taskList = wrapper.findComponent({ name: 'CDTaskList' })
      await taskList.vm.$emit('refresh')

      expect(mockCdStore.fetchTasks).toHaveBeenCalled()
    })

    it('处理加载错误', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockDevOpsStore.loadApplications.mockRejectedValue(new Error('网络错误'))

      await wrapper.vm.loadApplications()

      expect(consoleSpy).toHaveBeenCalledWith('加载应用失败:', expect.any(Error))
      consoleSpy.mockRestore()
    })
  })

  describe('计算属性', () => {
    it('正确计算面包屑导航', () => {
      const breadcrumbItems = wrapper.vm.breadcrumbItems
      expect(breadcrumbItems).toHaveLength(3)
      expect(breadcrumbItems[0]).toEqual({ title: 'DevOps 管理', path: '/devops', icon: 'home' })
      expect(breadcrumbItems[1]).toEqual({ title: 'CI/CD 管理', path: '/devops/cicd', icon: 'cicd' })
      expect(breadcrumbItems[2]).toEqual({ title: 'CD 管理', active: true, icon: 'cd' })
    })

    it('正确计算任务统计', () => {
      const stats = wrapper.vm.stats
      expect(stats.total).toBe(3)
      expect(stats.deploying).toBe(1)
      expect(stats.deployed).toBe(0)
      expect(stats.failed).toBe(1)
    })

    it('正确获取任务和应用数据', () => {
      expect(wrapper.vm.tasks).toEqual(mockCdStore.tasks)
      expect(wrapper.vm.applications).toEqual(mockDevOpsStore.applications)
    })
  })

  describe('错误处理', () => {
    it('处理任务创建错误', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockCdStore.createTask.mockRejectedValue(new Error('创建失败'))

      const taskData = { name: '测试任务', applicationId: 1 }
      await wrapper.vm.handleSubmitTask(taskData)

      expect(consoleSpy).toHaveBeenCalledWith('保存任务失败:', expect.any(Error))
      consoleSpy.mockRestore()
    })

    it('处理任务删除错误', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockCdStore.deleteTask.mockRejectedValue(new Error('删除失败'))

      wrapper.vm.deleteTarget = mockCdStore.tasks[0]
      await wrapper.vm.confirmDelete()

      expect(consoleSpy).toHaveBeenCalledWith('删除任务失败:', expect.any(Error))
      expect(wrapper.vm.deleting).toBe(false)
      consoleSpy.mockRestore()
    })

    it('处理任务部署错误', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockCdStore.deployTask.mockRejectedValue(new Error('部署失败'))

      wrapper.vm.deployTarget = mockCdStore.tasks[0]
      await wrapper.vm.confirmDeploy()

      expect(consoleSpy).toHaveBeenCalledWith('部署任务失败:', expect.any(Error))
      expect(wrapper.vm.deploying).toBe(false)
      consoleSpy.mockRestore()
    })

    it('处理任务回滚错误', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockCdStore.rollbackTask.mockRejectedValue(new Error('回滚失败'))

      wrapper.vm.rollbackTarget = mockCdStore.tasks[0]
      await wrapper.vm.confirmRollback()

      expect(consoleSpy).toHaveBeenCalledWith('回滚任务失败:', expect.any(Error))
      expect(wrapper.vm.rollingBack).toBe(false)
      consoleSpy.mockRestore()
    })

    it('处理空目标任务的删除操作', async () => {
      wrapper.vm.deleteTarget = null
      await wrapper.vm.confirmDelete()

      expect(mockCdStore.deleteTask).not.toHaveBeenCalled()
    })

    it('处理空目标任务的部署操作', async () => {
      wrapper.vm.deployTarget = null
      await wrapper.vm.confirmDeploy()

      expect(mockCdStore.deployTask).not.toHaveBeenCalled()
    })

    it('处理空目标任务的回滚操作', async () => {
      wrapper.vm.rollbackTarget = null
      await wrapper.vm.confirmRollback()

      expect(mockCdStore.rollbackTask).not.toHaveBeenCalled()
    })
  })

  describe('状态管理', () => {
    it('正确管理loading状态', async () => {
      expect(wrapper.vm.loading).toBe(false)
      expect(wrapper.vm.deleting).toBe(false)
      expect(wrapper.vm.deploying).toBe(false)
      expect(wrapper.vm.rollingBack).toBe(false)
    })

    it('正确管理模态框状态', () => {
      expect(wrapper.vm.showCreateModal).toBe(false)
      expect(wrapper.vm.showEditModal).toBe(false)
      expect(wrapper.vm.showDeleteDialog).toBe(false)
      expect(wrapper.vm.showDeployDialog).toBe(false)
      expect(wrapper.vm.showRollbackDialog).toBe(false)
      expect(wrapper.vm.showLogsModal).toBe(false)
    })

    it('正确管理目标任务状态', () => {
      expect(wrapper.vm.editingTask).toBeNull()
      expect(wrapper.vm.deleteTarget).toBeNull()
      expect(wrapper.vm.deployTarget).toBeNull()
      expect(wrapper.vm.rollbackTarget).toBeNull()
      expect(wrapper.vm.currentTask).toBeNull()
    })
  })
})
