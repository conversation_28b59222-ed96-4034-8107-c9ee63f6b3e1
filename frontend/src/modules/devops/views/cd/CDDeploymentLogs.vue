<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <div class="header-info">
          <h2>CD 任务部署日志</h2>
          <div class="task-info">
            <span class="task-name">{{ task?.name }}</span>
            <StatusTag v-if="currentDeployment" :status="currentDeployment.status" />
          </div>
        </div>
        <button @click="$emit('close')" class="close-button">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <!-- 部署实例选择 -->
        <div class="deployment-selector">
          <div class="selector-header">
            <h3>部署实例</h3>
            <div class="selector-actions">
              <button
                @click="refreshDeployments"
                :disabled="loadingDeployments"
                class="btn btn-sm btn-secondary"
              >
                <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
                刷新
              </button>
            </div>
          </div>
          
          <div class="deployments-list">
            <div
              v-for="deployment in deployments"
              :key="deployment.id"
              :class="['deployment-item', { active: selectedDeploymentId === deployment.instanceId }]"
              @click="selectDeployment(deployment)"
            >
              <div class="deployment-info">
                <div class="deployment-id">{{ deployment.instanceId }}</div>
                <div class="deployment-time">
                  {{ formatTime(deployment.startTime) }}
                  <span v-if="deployment.endTime">
                    - {{ formatTime(deployment.endTime) }}
                  </span>
                </div>
                <div class="deployment-version" v-if="deployment.version">
                  版本: {{ deployment.version }}
                </div>
              </div>
              <div class="deployment-status">
                <StatusTag :status="deployment.status" />
              </div>
              <div class="deployment-duration">
                {{ formatDuration(deployment.startTime, deployment.endTime) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 部署阶段 -->
        <div class="deployment-stages" v-if="currentDeployment">
          <div class="stages-header">
            <h3>部署阶段</h3>
          </div>
          <div class="stages-list">
            <div
              v-for="stage in deploymentStages"
              :key="stage.name"
              :class="['stage-item', `stage-${stage.status.toLowerCase()}`]"
            >
              <div class="stage-icon">
                <svg v-if="stage.status === 'COMPLETED'" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <svg v-else-if="stage.status === 'RUNNING'" class="animate-spin" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
                <svg v-else-if="stage.status === 'FAILED'" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
                <svg v-else viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="stage-content">
                <div class="stage-name">{{ stage.name }}</div>
                <div class="stage-description">{{ stage.description }}</div>
                <div class="stage-time" v-if="stage.startTime">
                  {{ formatTime(stage.startTime as string) }}
                  <span v-if="stage.endTime">
                    - {{ formatTime(stage.endTime as string) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 日志查看器 -->
        <div class="logs-section">
          <div class="logs-header">
            <h3>部署日志</h3>
            <div class="logs-actions">
              <label class="auto-scroll-toggle">
                <input
                  v-model="autoScroll"
                  type="checkbox"
                />
                <span>自动滚动</span>
              </label>
              <button
                @click="downloadLogs"
                :disabled="!currentDeployment"
                class="btn btn-sm btn-secondary"
              >
                <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
                下载日志
              </button>
              <button
                @click="refreshLogs"
                :disabled="loadingLogs"
                class="btn btn-sm btn-secondary"
              >
                <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
                刷新日志
              </button>
            </div>
          </div>

          <div class="logs-viewer" ref="logsContainer">
            <div v-if="loadingLogs" class="logs-loading">
              <svg class="animate-spin" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
              </svg>
              加载日志中...
            </div>
            <div v-else-if="!currentLogs" class="logs-empty">
              请选择一个部署实例查看日志
            </div>
            <div v-else class="logs-content">
              <div
                v-for="(line, index) in logLines"
                :key="index"
                :class="['log-line', getLogLineClass(line)]"
              >
                <span class="log-timestamp">{{ extractTimestamp(line) }}</span>
                <span class="log-level">{{ extractLevel(line) }}</span>
                <span class="log-message">{{ extractMessage(line) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 部署控制 -->
        <div v-if="currentDeployment && currentDeployment.status === 'DEPLOYING'" class="deployment-controls">
          <button
            @click="stopDeployment"
            :disabled="stopping"
            class="btn btn-danger"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clip-rule="evenodd" />
            </svg>
            {{ stopping ? '停止中...' : '停止部署' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onUnmounted } from 'vue'
import StatusTag from '@/components/StatusTag.vue'
import { useCdStore } from '@/modules/devops/stores/cd'
import type { DevOpsCdTask, DevOpsCdTaskInstance } from '@/modules/devops/types/devops'

// Props
interface Props {
  visible: boolean
  task?: DevOpsCdTask | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

const cdStore = useCdStore()

// 响应式数据
const loadingDeployments = ref(false)
const loadingLogs = ref(false)
const stopping = ref(false)
const autoScroll = ref(true)
const selectedDeploymentId = ref<string>('')
const deployments = ref<DevOpsCdTaskInstance[]>([])
const currentLogs = ref<string>('')
const logsContainer = ref<HTMLElement>()
const logRefreshInterval = ref<number>()

// 部署阶段数据
const deploymentStages = ref([
  {
    name: '准备阶段',
    description: '准备部署环境和资源',
    status: 'COMPLETED',
    startTime: '2024-01-15T10:30:00Z',
    endTime: '2024-01-15T10:30:30Z'
  },
  {
    name: '镜像拉取',
    description: '拉取应用镜像',
    status: 'COMPLETED',
    startTime: '2024-01-15T10:30:30Z',
    endTime: '2024-01-15T10:31:00Z'
  },
  {
    name: '服务部署',
    description: '部署应用服务',
    status: 'RUNNING',
    startTime: '2024-01-15T10:31:00Z'
  },
  {
    name: '健康检查',
    description: '检查服务健康状态',
    status: 'PENDING'
  },
  {
    name: '流量切换',
    description: '切换流量到新版本',
    status: 'PENDING'
  }
])

// 计算属性
const currentDeployment = computed(() =>
  deployments.value.find(deployment => deployment.instanceId === selectedDeploymentId.value)
)

const logLines = computed(() => {
  if (!currentLogs.value) return []
  return currentLogs.value.split('\n').filter(line => line.trim())
})

// 方法
const handleOverlayClick = () => {
  emit('close')
}

const loadDeployments = async () => {
  if (!props.task?.id) return

  loadingDeployments.value = true
  try {
    // 使用实际的store方法获取任务实例
    const taskInstances = await cdStore.fetchTaskInstances(props.task.id)
    deployments.value = taskInstances

    // 默认选择最新的实例
    if (deployments.value.length > 0) {
      selectDeployment(deployments.value[0])
    }
  } catch (error) {
    console.error('加载部署实例失败:', error)
    // 如果API调用失败，使用模拟数据作为后备
    deployments.value = [
      {
        id: 1,
        cdTaskId: props.task.id,
        instanceId: 'cd-20240115-001',
        status: 'DEPLOYED',
        startTime: '2024-01-15T10:30:00Z',
        endTime: '2024-01-15T10:35:00Z',
        version: 'v1.2.0',
        logs: '',
        userId: 1,
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-15T10:35:00Z'
      },
      {
        id: 2,
        cdTaskId: props.task.id,
        instanceId: 'cd-20240115-002',
        status: 'DEPLOYING',
        startTime: '2024-01-15T11:00:00Z',
        version: 'v1.3.0',
        logs: '',
        userId: 1,
        createdAt: '2024-01-15T11:00:00Z',
        updatedAt: '2024-01-15T11:00:00Z'
      }
    ]

    if (deployments.value.length > 0) {
      selectDeployment(deployments.value[0])
    }
  } finally {
    loadingDeployments.value = false
  }
}

const refreshDeployments = () => {
  loadDeployments()
}

const selectDeployment = (deployment: DevOpsCdTaskInstance) => {
  selectedDeploymentId.value = deployment.instanceId
  loadLogs()
}

const loadLogs = async () => {
  if (!selectedDeploymentId.value) return

  loadingLogs.value = true
  try {
    // 使用实际的store方法获取任务日志
    const logs = await cdStore.fetchTaskLogs(selectedDeploymentId.value)
    currentLogs.value = logs

    if (autoScroll.value) {
      await nextTick()
      scrollToBottom()
    }
  } catch (error) {
    console.error('加载部署日志失败:', error)
    // 如果API调用失败，使用模拟数据作为后备
    currentLogs.value = `2024-01-15T10:30:00Z INFO 开始执行 CD 部署任务...
2024-01-15T10:30:05Z INFO 准备部署环境...
2024-01-15T10:30:10Z INFO 环境准备完成
2024-01-15T10:30:15Z INFO 开始拉取镜像...
2024-01-15T10:30:45Z INFO 镜像拉取完成: myapp:v1.3.0
2024-01-15T10:31:00Z INFO 开始部署服务...
2024-01-15T10:31:30Z INFO 创建 Deployment: myapp-deployment
2024-01-15T10:32:00Z INFO 创建 Service: myapp-service
2024-01-15T10:32:30Z INFO 等待 Pod 就绪...
2024-01-15T10:33:00Z INFO Pod 就绪: myapp-deployment-7d4b8c9f8d-abc123
2024-01-15T10:33:30Z INFO 开始健康检查...
2024-01-15T10:34:00Z INFO 健康检查通过
2024-01-15T10:34:30Z INFO 开始流量切换...
2024-01-15T10:35:00Z INFO 部署完成`

    if (autoScroll.value) {
      await nextTick()
      scrollToBottom()
    }
  } finally {
    loadingLogs.value = false
  }
}

const refreshLogs = () => {
  loadLogs()
}

const downloadLogs = () => {
  if (!currentLogs.value || !currentDeployment.value) return

  const blob = new Blob([currentLogs.value], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `cd-logs-${currentDeployment.value.instanceId}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const stopDeployment = async () => {
  if (!currentDeployment.value) return

  stopping.value = true
  try {
    await cdStore.stopInstance(currentDeployment.value.instanceId)
    // 刷新部署状态
    await loadDeployments()
  } catch (error) {
    console.error('停止部署失败:', error)
  } finally {
    stopping.value = false
  }
}

const scrollToBottom = () => {
  if (logsContainer.value) {
    logsContainer.value.scrollTop = logsContainer.value.scrollHeight
  }
}

const formatTime = (time?: string) => {
  if (!time) return ''
  return new Date(time).toLocaleString()
}

const formatDuration = (startTime?: string, endTime?: string) => {
  if (!startTime) return ''
  
  const start = new Date(startTime)
  const end = endTime ? new Date(endTime) : new Date()
  const duration = end.getTime() - start.getTime()
  
  const minutes = Math.floor(duration / 60000)
  const seconds = Math.floor((duration % 60000) / 1000)
  
  if (minutes > 0) {
    return `${minutes}分${seconds}秒`
  }
  return `${seconds}秒`
}

const extractTimestamp = (line: string) => {
  const match = line.match(/^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z)/)
  return match ? match[1] : ''
}

const extractLevel = (line: string) => {
  const match = line.match(/\s(INFO|WARN|ERROR|DEBUG)\s/)
  return match ? match[1] : ''
}

const extractMessage = (line: string) => {
  const match = line.match(/\s(?:INFO|WARN|ERROR|DEBUG)\s(.+)$/)
  return match ? match[1] : line
}

const getLogLineClass = (line: string) => {
  const level = extractLevel(line)
  return {
    'log-info': level === 'INFO',
    'log-warn': level === 'WARN',
    'log-error': level === 'ERROR',
    'log-debug': level === 'DEBUG'
  }
}

const startLogRefresh = () => {
  if (currentDeployment.value?.status === 'DEPLOYING') {
    logRefreshInterval.value = window.setInterval(() => {
      loadLogs()
    }, 5000) // 每5秒刷新一次日志
  }
}

const stopLogRefresh = () => {
  if (logRefreshInterval.value) {
    clearInterval(logRefreshInterval.value)
    logRefreshInterval.value = undefined
  }
}

// 监听器
watch(() => props.visible, (visible) => {
  if (visible && props.task) {
    loadDeployments()
  } else {
    stopLogRefresh()
  }
})

watch(currentDeployment, (deployment) => {
  stopLogRefresh()
  if (deployment?.status === 'DEPLOYING') {
    startLogRefresh()
  }
})

watch(autoScroll, (enabled) => {
  if (enabled) {
    scrollToBottom()
  }
})

// 生命周期
onUnmounted(() => {
  stopLogRefresh()
})
</script>

<style scoped>
/* 模态框样式 */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.header-info {
  @apply flex-1;
}

.header-info h2 {
  @apply text-xl font-semibold text-gray-900 m-0 mb-2;
}

.task-info {
  @apply flex items-center gap-3;
}

.task-name {
  @apply text-sm text-gray-600 font-medium;
}

.close-button {
  @apply p-2 text-gray-400 hover:text-gray-600 transition-colors;
}

.close-button svg {
  @apply w-5 h-5;
}

.modal-body {
  @apply p-6 overflow-y-auto max-h-[calc(90vh-120px)] space-y-6;
}

/* 部署实例选择器 */
.deployment-selector {
  @apply bg-gray-50 rounded-lg p-4;
}

.selector-header {
  @apply flex items-center justify-between mb-4;
}

.selector-header h3 {
  @apply text-lg font-medium text-gray-900 m-0;
}

.selector-actions {
  @apply flex gap-2;
}

.deployments-list {
  @apply space-y-2 max-h-40 overflow-y-auto;
}

.deployment-item {
  @apply flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200 cursor-pointer hover:border-blue-300 transition-colors;
}

.deployment-item.active {
  @apply border-blue-500 bg-blue-50;
}

.deployment-info {
  @apply flex-1;
}

.deployment-id {
  @apply font-medium text-gray-900;
}

.deployment-time {
  @apply text-sm text-gray-600 mt-1;
}

.deployment-version {
  @apply text-xs text-gray-500 font-mono mt-1;
}

.deployment-status {
  @apply mx-4;
}

.deployment-duration {
  @apply text-sm text-gray-500 font-mono;
}

/* 部署阶段 */
.deployment-stages {
  @apply bg-white border border-gray-200 rounded-lg overflow-hidden;
}

.stages-header {
  @apply p-4 border-b border-gray-200 bg-gray-50;
}

.stages-header h3 {
  @apply text-lg font-medium text-gray-900 m-0;
}

.stages-list {
  @apply p-4 space-y-4;
}

.stage-item {
  @apply flex items-start gap-4;
}

.stage-icon {
  @apply w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0;
}

.stage-icon svg {
  @apply w-5 h-5;
}

.stage-completed .stage-icon {
  @apply bg-green-100 text-green-600;
}

.stage-running .stage-icon {
  @apply bg-blue-100 text-blue-600;
}

.stage-failed .stage-icon {
  @apply bg-red-100 text-red-600;
}

.stage-pending .stage-icon {
  @apply bg-gray-100 text-gray-400;
}

.stage-content {
  @apply flex-1;
}

.stage-name {
  @apply font-medium text-gray-900 mb-1;
}

.stage-description {
  @apply text-sm text-gray-600 mb-2;
}

.stage-time {
  @apply text-xs text-gray-500;
}

/* 日志查看器 */
.logs-section {
  @apply bg-white border border-gray-200 rounded-lg overflow-hidden;
}

.logs-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50;
}

.logs-header h3 {
  @apply text-lg font-medium text-gray-900 m-0;
}

.logs-actions {
  @apply flex items-center gap-4;
}

.auto-scroll-toggle {
  @apply flex items-center gap-2 cursor-pointer;
}

.auto-scroll-toggle input[type="checkbox"] {
  @apply w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500;
}

.auto-scroll-toggle span {
  @apply text-sm text-gray-700;
}

.logs-viewer {
  @apply h-96 overflow-y-auto bg-gray-900 text-gray-100 font-mono text-sm;
}

.logs-loading {
  @apply flex items-center justify-center h-full text-gray-400;
}

.logs-loading svg {
  @apply w-5 h-5 mr-2;
}

.logs-empty {
  @apply flex items-center justify-center h-full text-gray-400;
}

.logs-content {
  @apply p-4 space-y-1;
}

.log-line {
  @apply flex items-start gap-3 py-1 hover:bg-gray-800 transition-colors;
}

.log-timestamp {
  @apply text-gray-400 text-xs flex-shrink-0 w-20;
}

.log-level {
  @apply text-xs font-bold flex-shrink-0 w-12;
}

.log-message {
  @apply flex-1 break-words;
}

.log-info .log-level {
  @apply text-blue-400;
}

.log-warn .log-level {
  @apply text-yellow-400;
}

.log-error .log-level {
  @apply text-red-400;
}

.log-debug .log-level {
  @apply text-gray-400;
}

/* 部署控制 */
.deployment-controls {
  @apply flex justify-center pt-4 border-t border-gray-200;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-sm {
  @apply px-3 py-1.5 text-sm;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.btn-icon {
  @apply w-4 h-4;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
