<template>
  <div class="cd-management">
    <!-- 面包屑导航 -->
    <Breadcrumb :items="breadcrumbItems" />

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h1>CD 管理</h1>
          <p>配置和管理持续部署流水线，自动化应用部署、回滚和环境管理</p>
        </div>
        <div class="header-actions">
          <button
            @click="showCreateModal = true"
            class="btn btn-primary"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            创建CD任务
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card cd-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm1 5a1 1 0 000 2h4.586l-2.293 2.293a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 10-1.414 1.414L9.586 7H5z" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ cdStore.tasks.length }}</div>
          <div class="stat-label">CD 任务总数</div>
          <div class="stat-detail">
            <span class="deploying">{{ cdStore.deployingTasks.length }} 部署中</span>
            <span class="deployed">{{ cdStore.deployedTasks.length }} 已部署</span>
          </div>
        </div>
      </div>

      <div class="stat-card deployment-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ deploymentTasks.length }}</div>
          <div class="stat-label">部署任务</div>
          <div class="stat-detail">
            <span class="active">{{ activeDeployments.length }} 活跃</span>
            <span class="failed">{{ failedDeployments.length }} 失败</span>
          </div>
        </div>
      </div>

      <div class="stat-card environment-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ Object.keys(cdStore.tasksByEnvironment).length }}</div>
          <div class="stat-label">部署环境</div>
          <div class="stat-detail">
            <span class="production">{{ productionTasks.length }} 生产</span>
            <span class="staging">{{ stagingTasks.length }} 测试</span>
          </div>
        </div>
      </div>

      <div class="stat-card success-rate-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ deploymentSuccessRate }}%</div>
          <div class="stat-label">部署成功率</div>
          <div class="stat-detail">
            <span class="success">{{ cdStore.deployedTasks.length }} 成功</span>
            <span class="failed">{{ cdStore.failedTasks.length }} 失败</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 部署策略切换 -->
    <div class="strategy-tabs">
      <button
        v-for="tab in strategyTabs"
        :key="tab.key"
        :class="['tab-button', { active: activeTab === tab.key }]"
        @click="activeTab = tab.key"
      >
        <svg class="tab-icon" viewBox="0 0 20 20" fill="currentColor">
          <path :d="tab.icon" />
        </svg>
        {{ tab.label }}
        <span class="tab-count">{{ getTabCount(tab.key) }}</span>
      </button>
    </div>

    <!-- 筛选器 -->
    <div class="filters-section">
      <div class="filter-group">
        <label class="filter-label">按应用筛选：</label>
        <select
          v-model="selectedApplicationId"
          @change="handleApplicationFilter"
          class="filter-select"
        >
          <option value="">全部应用</option>
          <option
            v-for="application in applications"
            :key="application.id"
            :value="application.id"
          >
            {{ application.name }}
          </option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">状态筛选：</label>
        <select
          v-model="selectedStatus"
          @change="handleStatusFilter"
          class="filter-select"
        >
          <option value="">全部状态</option>
          <option value="ACTIVE">活跃</option>
          <option value="DEPLOYING">部署中</option>
          <option value="DEPLOYED">已部署</option>
          <option value="FAILED">失败</option>
          <option value="STOPPED">已停止</option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">环境筛选：</label>
        <select
          v-model="selectedEnvironment"
          @change="handleEnvironmentFilter"
          class="filter-select"
        >
          <option value="">全部环境</option>
          <option value="development">开发环境</option>
          <option value="staging">测试环境</option>
          <option value="production">生产环境</option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">部署策略：</label>
        <select
          v-model="selectedStrategy"
          @change="handleStrategyFilter"
          class="filter-select"
        >
          <option value="">全部策略</option>
          <option value="rolling">滚动部署</option>
          <option value="blue-green">蓝绿部署</option>
          <option value="canary">金丝雀部署</option>
        </select>
      </div>
    </div>

    <!-- 数据表格 -->
    <DataTable
      :data="filteredTasks"
      :columns="tableColumns"
      :loading="cdStore.loading"
      title="CD 任务列表"
      searchable
      search-placeholder="搜索CD任务名称或描述..."
      @search="handleSearch"
      @row-click="handleRowClick"
    >
      <template #toolbar-actions>
        <button
          @click="refreshData"
          :disabled="cdStore.loading"
          class="btn btn-secondary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          刷新
        </button>
      </template>

      <template #cell-applicationName="{ record }">
        <span class="application-link" @click="viewApplication(record.applicationId)">
          {{ getApplicationName(record.applicationId) }}
        </span>
      </template>

      <template #cell-environment="{ value }">
        <span class="environment-badge" :class="`env-${value?.toLowerCase()}`">
          {{ getEnvironmentLabel(value) }}
        </span>
      </template>

      <template #cell-deploymentStrategy="{ value }">
        <span class="strategy-badge" :class="`strategy-${value?.toLowerCase()}`">
          {{ getStrategyLabel(value) }}
        </span>
      </template>

      <template #cell-status="{ value }">
        <StatusTag :status="value" />
      </template>

      <template #cell-version="{ value }">
        <span class="version-text">{{ value || '-' }}</span>
      </template>

      <template #cell-deployedAt="{ value }">
        {{ value ? formatDate(value) : '-' }}
      </template>

      <template #actions="{ record }">
        <div class="action-buttons">
          <button
            @click="viewTask(record)"
            class="action-btn action-btn-view"
            title="查看详情"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
              <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            v-if="canViewLogs(record)"
            @click="viewLogs(record)"
            class="action-btn action-btn-logs"
            title="查看日志"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
              <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            v-if="canDeployTask(record)"
            @click="deployTask(record)"
            class="action-btn action-btn-deploy"
            title="部署"
            :disabled="deploying"
          >
            <svg v-if="deploying" class="animate-spin" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 3a7 7 0 100 14 7 7 0 000-14zM2 10a8 8 0 1116 0 8 8 0 01-16 0z" opacity="0.3"/>
              <path d="M10 2a8 8 0 018 8" fill="currentColor">
                <animateTransform attributeName="transform" type="rotate" from="0 10 10" to="360 10 10" dur="1s" repeatCount="indefinite"/>
              </path>
            </svg>
            <svg v-else viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            v-if="canRollbackTask(record)"
            @click="rollbackTask(record)"
            class="action-btn action-btn-rollback"
            title="回滚"
            :disabled="rollingBack"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            v-if="canStopTask(record)"
            @click="stopTask(record)"
            class="action-btn action-btn-stop"
            title="停止部署"
            :disabled="stopping"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            @click="editTask(record)"
            class="action-btn action-btn-edit"
            title="编辑任务"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
          </button>
          <button
            @click="deleteTask(record)"
            class="action-btn action-btn-delete"
            title="删除任务"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </template>
    </DataTable>

    <!-- 创建/编辑任务模态框 -->
    <CDTaskModal
      v-if="showCreateModal || showEditModal"
      :visible="showCreateModal || showEditModal"
      :task="editingTask"
      :applications="applications"
      @close="closeModal"
      @submit="handleTaskSubmit"
    />

    <!-- 日志查看模态框 -->
    <TaskLogsModal
      v-if="showLogsModal"
      :visible="showLogsModal"
      :task="currentTask"
      @close="closeLogsModal"
    />

    <!-- 确认删除对话框 -->
    <ConfirmDialog
      :visible="showDeleteDialog"
      title="确认删除CD任务"
      :message="`确定要删除CD任务 &quot;${deleteTarget?.name}&quot; 吗？此操作不可撤销。`"
      type="danger"
      confirm-text="删除"
      :loading="deleting"
      @confirm="confirmDelete"
      @cancel="showDeleteDialog = false"
    />

    <!-- 确认部署对话框 -->
    <ConfirmDialog
      :visible="showDeployDialog"
      title="确认部署"
      :message="`确定要部署任务 &quot;${deployTarget?.name}&quot; 到 ${getEnvironmentLabel(deployTarget?.configuration?.targetEnvironment)} 环境吗？`"
      type="warning"
      confirm-text="部署"
      :loading="deploying"
      @confirm="confirmDeploy"
      @cancel="showDeployDialog = false"
    />

    <!-- 确认回滚对话框 -->
    <ConfirmDialog
      :visible="showRollbackDialog"
      title="确认回滚"
      :message="`确定要回滚任务 &quot;${rollbackTarget?.name}&quot; 吗？这将回滚到上一个版本。`"
      type="danger"
      confirm-text="回滚"
      :loading="rollingBack"
      @confirm="confirmRollback"
      @cancel="showRollbackDialog = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import DataTable from '@/components/DataTable.vue'
import StatusTag from '@/components/StatusTag.vue'
import Breadcrumb from '@/components/Breadcrumb.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import CDTaskModal from '@/modules/devops/components/CDTaskModal.vue'
import TaskLogsModal from '@/modules/devops/components/TaskLogsModal.vue'
import { useCdStore } from '@/modules/devops/stores/cd'
import { useDevOpsStore } from '@/modules/devops/stores/devops'
import type { DevOpsCdTask, BreadcrumbItem, TableColumn } from '@/modules/devops/types/devops'

const router = useRouter()
const cdStore = useCdStore()
const devopsStore = useDevOpsStore()

// 响应式数据
const deploying = ref(false)
const rollingBack = ref(false)
const stopping = ref(false)
const deleting = ref(false)
const searchQuery = ref('')
const selectedApplicationId = ref<number | ''>('')
const selectedStatus = ref('')
const selectedEnvironment = ref('')
const selectedStrategy = ref('')
const activeTab = ref('all')

// 模态框状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteDialog = ref(false)
const showDeployDialog = ref(false)
const showRollbackDialog = ref(false)
const showLogsModal = ref(false)
const currentTask = ref<DevOpsCdTask | null>(null)
const editingTask = ref<DevOpsCdTask | null>(null)
const deleteTarget = ref<DevOpsCdTask | null>(null)
const deployTarget = ref<DevOpsCdTask | null>(null)
const rollbackTarget = ref<DevOpsCdTask | null>(null)

// 计算属性
const breadcrumbItems = computed<BreadcrumbItem[]>(() => [
  { title: 'DevOps 管理', path: '/devops', icon: 'home' },
  { title: 'CD 管理', active: true, icon: 'cd' }
])

const applications = computed(() => devopsStore.applications || [])

const deploymentTasks = computed(() =>
  cdStore.tasks.filter(task => task.deploymentStrategy)
)

const activeDeployments = computed(() =>
  deploymentTasks.value.filter(task => task.status === 'DEPLOYING')
)

const failedDeployments = computed(() =>
  deploymentTasks.value.filter(task => task.status === 'FAILED')
)

const productionTasks = computed(() =>
  cdStore.tasks.filter(task => task.configuration.targetEnvironment === 'production')
)

const stagingTasks = computed(() =>
  cdStore.tasks.filter(task =>task.configuration.targetEnvironment === 'staging')
)

const deploymentSuccessRate = computed(() => {
  const total = cdStore.tasks.length
  if (total === 0) return 0
  const successful = cdStore.deployedTasks.length
  return Math.round((successful / total) * 100)
})

const strategyTabs = [
  { key: 'all', label: '全部任务', icon: 'M4 6a2 2 0 012-2h8a2 2 0 012 2v7a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM6 8a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 4a1 1 0 100 2h6a1 1 0 100-2H7z' },
  { key: 'rolling', label: '滚动部署', icon: 'M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z' },
  { key: 'blue-green', label: '蓝绿部署', icon: 'M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z' },
  { key: 'canary', label: '金丝雀部署', icon: 'M9 12a3 3 0 003-3m-3 3a3 3 0 01-3-3m3 3v6m-3-9a3 3 0 013-3m-3 3h6m-6 0a3 3 0 01-3-3m3 3v6' }
]

const tableColumns: TableColumn[] = [
  { key: 'name', title: '任务名称', sortable: true },
  { key: 'applicationName', title: '关联应用', width: '150px' },
  { key: 'environment', title: '部署环境', width: '100px' },
  { key: 'deploymentStrategy', title: '部署策略', width: '120px' },
  { key: 'status', title: '状态', width: '120px' },
  { key: 'version', title: '版本', width: '100px' },
  { key: 'deployedAt', title: '部署时间', width: '180px', sortable: true }
]

const filteredTasks = computed(() => {
  let result = [...cdStore.tasks]

  // 按部署策略筛选
  if (activeTab.value !== 'all') {
    result = result.filter(task => task.deploymentStrategy === activeTab.value)
  }

  // 按应用筛选
  if (selectedApplicationId.value) {
    result = result.filter(task => task.applicationId === selectedApplicationId.value)
  }

  // 按状态筛选
  if (selectedStatus.value) {
    result = result.filter(task => task.status === selectedStatus.value)
  }

  // 按环境筛选
  if (selectedEnvironment.value) {
    result = result.filter(task => task.configuration.targetEnvironment === selectedEnvironment.value)
  }

  // 按部署策略筛选
  if (selectedStrategy.value) {
    result = result.filter(task => task.deploymentStrategy === selectedStrategy.value)
  }

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(task =>
      task.name.toLowerCase().includes(query) ||
      (task.description && task.description.toLowerCase().includes(query))
    )
  }

  return result
})

// 方法
const refreshData = async () => {
  await cdStore.fetchTasks()
  // 如果需要应用数据，可以从devopsStore获取
}

const handleSearch = (query: string) => {
  searchQuery.value = query
}

const handleApplicationFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleStatusFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleEnvironmentFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleStrategyFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleRowClick = (task: DevOpsCdTask) => {
  viewTask(task)
}

const getTabCount = (tabKey: string) => {
  if (tabKey === 'all') return cdStore.tasks.length
  return cdStore.tasks.filter(task => task.deploymentStrategy === tabKey).length
}

const getApplicationName = (applicationId: number) => {
  const application = applications.value.find(a => a.id === applicationId)
  return application?.name || '未知应用'
}

const getEnvironmentLabel = (env: string) => {
  const envMap: Record<string, string> = {
    development: '开发环境',
    staging: '测试环境',
    production: '生产环境'
  }
  return envMap[env] || env
}

const getStrategyLabel = (strategy: string) => {
  const strategyMap: Record<string, string> = {
    rolling: '滚动部署',
    'blue-green': '蓝绿部署',
    canary: '金丝雀部署'
  }
  return strategyMap[strategy] || strategy
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const viewApplication = (applicationId: number) => {
  router.push(`/devops/applications/${applicationId}`)
}

const viewTask = (task: DevOpsCdTask) => {
  router.push(`/devops/cd/${task.id}`)
}

const canViewLogs = (task: DevOpsCdTask) => {
  return task && task.status && ['DEPLOYING', 'DEPLOYED', 'FAILED'].includes(task.status)
}

const canDeployTask = (task: DevOpsCdTask) => {
  return task && task.status && ['ACTIVE', 'FAILED', 'STOPPED'].includes(task.status)
}

const canRollbackTask = (task: DevOpsCdTask) => {
  return task && task.status === 'DEPLOYED'
}

const canStopTask = (task: DevOpsCdTask) => {
  return task && task.status === 'DEPLOYING'
}

const viewLogs = (task: DevOpsCdTask) => {
  currentTask.value = task
  showLogsModal.value = true
}

const deployTask = (task: DevOpsCdTask) => {
  deployTarget.value = task
  showDeployDialog.value = true
}

const rollbackTask = (task: DevOpsCdTask) => {
  rollbackTarget.value = task
  showRollbackDialog.value = true
}

const stopTask = async (task: DevOpsCdTask) => {
  if (!canStopTask(task)) return

  stopping.value = true
  try {
    // 这里需要实现停止部署的逻辑
    // await cdStore.stopTask(task.id!)
    console.log('停止部署:', task.name)
  } catch (error) {
    console.error('停止CD任务失败:', error)
  } finally {
    stopping.value = false
  }
}

const editTask = (task: DevOpsCdTask) => {
  editingTask.value = task
  showEditModal.value = true
}

const deleteTask = (task: DevOpsCdTask) => {
  deleteTarget.value = task
  showDeleteDialog.value = true
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  editingTask.value = null
}

const closeLogsModal = () => {
  showLogsModal.value = false
  currentTask.value = null
}

const handleTaskSubmit = async (taskData: any) => {
  try {
    if (editingTask.value) {
      await cdStore.updateTask(editingTask.value.id!, taskData)
    } else {
      await cdStore.createTask(taskData)
    }
    closeModal()
  } catch (error) {
    console.error('保存CD任务失败:', error)
  }
}

const confirmDelete = async () => {
  if (!deleteTarget.value) return

  deleting.value = true
  try {
    await cdStore.deleteTask(deleteTarget.value.id!)
    showDeleteDialog.value = false
    deleteTarget.value = null
  } catch (error) {
    console.error('删除CD任务失败:', error)
  } finally {
    deleting.value = false
  }
}

const confirmDeploy = async () => {
  if (!deployTarget.value) return

  deploying.value = true
  try {
    await cdStore.deployTask(deployTarget.value.id!)
    showDeployDialog.value = false
    deployTarget.value = null
  } catch (error) {
    console.error('部署CD任务失败:', error)
  } finally {
    deploying.value = false
  }
}

const confirmRollback = async () => {
  if (!rollbackTarget.value) return

  rollingBack.value = true
  try {
    await cdStore.rollbackTask(rollbackTarget.value.id!)
    showRollbackDialog.value = false
    rollbackTarget.value = null
  } catch (error) {
    console.error('回滚CD任务失败:', error)
  } finally {
    rollingBack.value = false
  }
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.cd-management {
  @apply p-6 bg-gray-50 min-h-screen;
}

/* 页面头部 */
.page-header {
  @apply mb-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.header-title h1 {
  @apply text-2xl font-bold text-gray-900 m-0;
}

.header-title p {
  @apply text-gray-600 mt-1 m-0;
}

.header-actions {
  @apply flex items-center gap-3;
}

/* 统计卡片 */
.stats-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6;
}

.stat-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 flex items-center gap-4;
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center;
}

.stat-icon svg {
  @apply w-6 h-6;
}

.cd-card .stat-icon {
  @apply bg-indigo-100 text-indigo-600;
}

.deployment-card .stat-icon {
  @apply bg-green-100 text-green-600;
}

.environment-card .stat-icon {
  @apply bg-blue-100 text-blue-600;
}

.success-rate-card .stat-icon {
  @apply bg-emerald-100 text-emerald-600;
}

.stat-content {
  @apply flex-1;
}

.stat-number {
  @apply text-2xl font-bold text-gray-900;
}

.stat-label {
  @apply text-sm text-gray-600 mt-1;
}

.stat-detail {
  @apply flex items-center gap-3 mt-2 text-xs;
}

.stat-detail .deploying {
  @apply text-blue-600;
}

.stat-detail .deployed {
  @apply text-green-600;
}

.stat-detail .active {
  @apply text-green-600;
}

.stat-detail .production {
  @apply text-red-600;
}

.stat-detail .staging {
  @apply text-yellow-600;
}

.stat-detail .success {
  @apply text-green-600;
}

.stat-detail .failed {
  @apply text-red-600;
}

/* 部署策略切换 */
.strategy-tabs {
  @apply flex items-center gap-2 mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-2;
}

.tab-button {
  @apply flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors;
}

.tab-button:not(.active) {
  @apply text-gray-600 hover:text-gray-900 hover:bg-gray-50;
}

.tab-button.active {
  @apply bg-indigo-100 text-indigo-700;
}

.tab-icon {
  @apply w-4 h-4;
}

.tab-count {
  @apply ml-1 px-2 py-0.5 text-xs bg-gray-200 text-gray-700 rounded-full;
}

.tab-button.active .tab-count {
  @apply bg-indigo-200 text-indigo-800;
}

/* 筛选器部分 */
.filters-section {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6 flex items-center gap-6;
}

.filter-group {
  @apply flex items-center gap-2;
}

.filter-label {
  @apply text-sm font-medium text-gray-700 whitespace-nowrap;
}

.filter-select {
  @apply px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none;
  min-width: 150px;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500;
}

.btn-secondary {
  @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500;
}

.btn-icon {
  @apply w-4 h-4;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 应用链接 */
.application-link {
  @apply text-indigo-600 hover:text-indigo-800 cursor-pointer hover:underline transition-colors;
}

/* 环境标签 */
.environment-badge {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium rounded-full;
}

.environment-badge.env-development {
  @apply bg-blue-100 text-blue-800;
}

.environment-badge.env-staging {
  @apply bg-yellow-100 text-yellow-800;
}

.environment-badge.env-production {
  @apply bg-red-100 text-red-800;
}

/* 部署策略标签 */
.strategy-badge {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium rounded-full;
}

.strategy-badge.strategy-rolling {
  @apply bg-green-100 text-green-800;
}

.strategy-badge.strategy-blue-green {
  @apply bg-blue-100 text-blue-800;
}

.strategy-badge.strategy-canary {
  @apply bg-purple-100 text-purple-800;
}

/* 版本文本 */
.version-text {
  @apply text-sm text-gray-600 font-mono;
}

/* 操作按钮 */
.action-buttons {
  @apply flex items-center gap-1;
}

.action-btn {
  @apply p-2 rounded-md transition-colors;
}

.action-btn svg {
  @apply w-4 h-4;
}

.action-btn-view {
  @apply text-indigo-600 hover:bg-indigo-50;
}

.action-btn-logs {
  @apply text-gray-600 hover:bg-gray-50;
}

.action-btn-deploy {
  @apply text-green-600 hover:bg-green-50;
}

.action-btn-rollback {
  @apply text-orange-600 hover:bg-orange-50;
}

.action-btn-stop {
  @apply text-red-600 hover:bg-red-50;
}

.action-btn-edit {
  @apply text-blue-600 hover:bg-blue-50;
}

.action-btn-delete {
  @apply text-red-600 hover:bg-red-50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cd-management {
    @apply p-4;
  }

  .header-content {
    @apply flex-col items-start gap-4;
  }

  .stats-grid {
    @apply grid-cols-1;
  }

  .filters-section {
    @apply flex-col items-start gap-4;
  }

  .strategy-tabs {
    @apply flex-wrap;
  }
}
</style>