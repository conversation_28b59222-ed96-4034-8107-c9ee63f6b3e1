<template>
  <div class="bg-white rounded-lg shadow-md p-6 border border-gray-200">
    <!-- Header -->
    <div class="flex justify-between items-start mb-4">
      <div>
        <h3 class="text-lg font-semibold text-gray-900">{{ serverInfo?.name || 'MCP Server' }}</h3>
        <p class="text-sm text-gray-600">{{ instance.sandboxId }}</p>
      </div>
      <div class="flex items-center space-x-2">
        <span :class="getStatusColor(instance.status)" 
              class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
          {{ instance.status }}
        </span>
        <button @click="toggleExpanded" class="text-gray-400 hover:text-gray-600">
          <svg :class="{ 'rotate-180': expanded }" class="w-5 h-5 transform transition-transform">
            <path fill="currentColor" d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Basic Info -->
    <div class="grid grid-cols-2 gap-4 mb-4">
      <div>
        <p class="text-xs text-gray-500">Started</p>
        <p class="text-sm font-medium">{{ formatDate(instance.startedAt) }}</p>
      </div>
      <div>
        <p class="text-xs text-gray-500">Uptime</p>
        <p class="text-sm font-medium">{{ formatUptime(instance.startedAt) }}</p>
      </div>
    </div>

    <!-- Resource Usage -->
    <div v-if="resourceUsage" class="mb-4">
      <div class="grid grid-cols-2 gap-4">
        <div>
          <p class="text-xs text-gray-500">Memory</p>
          <div class="flex items-center space-x-2">
            <div class="flex-1 bg-gray-200 rounded-full h-2">
              <div 
                :style="{ width: `${resourceUsage.memoryUsagePercent || 0}%` }"
                class="bg-blue-500 h-2 rounded-full"
              ></div>
            </div>
            <span class="text-xs text-gray-600">
              {{ Math.round(resourceUsage.memoryUsagePercent || 0) }}%
            </span>
          </div>
        </div>
        <div>
          <p class="text-xs text-gray-500">CPU</p>
          <div class="flex items-center space-x-2">
            <div class="flex-1 bg-gray-200 rounded-full h-2">
              <div 
                :style="{ width: `${Math.min(resourceUsage.cpuUsagePercent || 0, 100)}%` }"
                class="bg-green-500 h-2 rounded-full"
              ></div>
            </div>
            <span class="text-xs text-gray-600">
              {{ Math.round(resourceUsage.cpuUsagePercent || 0) }}%
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="flex justify-between items-center">
      <div class="flex space-x-2">
        <button 
          @click="viewTools"
          class="text-blue-600 hover:text-blue-800 text-sm font-medium"
        >
          View Tools
        </button>
        <button 
          @click="viewLogs"
          class="text-gray-600 hover:text-gray-800 text-sm font-medium"
        >
          Logs
        </button>
      </div>
      <div class="flex space-x-2">
        <button 
          @click="restart"
          :disabled="loading"
          class="px-3 py-1 text-xs bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 disabled:opacity-50"
        >
          Restart
        </button>
        <button 
          @click="stop"
          :disabled="loading"
          class="px-3 py-1 text-xs bg-red-100 text-red-800 rounded hover:bg-red-200 disabled:opacity-50"
        >
          Stop
        </button>
      </div>
    </div>

    <!-- Expanded Content -->
    <div v-if="expanded" class="mt-6 pt-6 border-t border-gray-200">
      <!-- Server Info -->
      <div v-if="serverInfo" class="mb-6">
        <h4 class="text-sm font-medium text-gray-900 mb-2">Server Information</h4>
        <div class="bg-gray-50 rounded-lg p-3 text-sm">
          <div class="grid grid-cols-2 gap-2">
            <div><span class="font-medium">Version:</span> {{ serverInfo.version }}</div>
            <div><span class="font-medium">Author:</span> {{ serverInfo.author || 'N/A' }}</div>
          </div>
          <div v-if="serverInfo.description" class="mt-2">
            <span class="font-medium">Description:</span> {{ serverInfo.description }}
          </div>
        </div>
      </div>

      <!-- Tools -->
      <div v-if="tools.length > 0" class="mb-6">
        <h4 class="text-sm font-medium text-gray-900 mb-2">Available Tools ({{ tools.length }})</h4>
        <div class="space-y-2">
          <div v-for="tool in tools" :key="tool.name" 
               class="bg-gray-50 rounded-lg p-3 cursor-pointer hover:bg-gray-100"
               @click="selectTool(tool)">
            <div class="flex justify-between items-start">
              <div>
                <p class="font-medium text-sm">{{ tool.name }}</p>
                <p class="text-xs text-gray-600">{{ tool.description }}</p>
              </div>
              <button class="text-blue-600 hover:text-blue-800 text-xs">
                Test
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Resource Details -->
      <div v-if="resourceUsage" class="mb-6">
        <h4 class="text-sm font-medium text-gray-900 mb-2">Resource Usage</h4>
        <div class="bg-gray-50 rounded-lg p-3 text-xs">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <p><span class="font-medium">Memory:</span> 
                {{ formatBytes(resourceUsage.memoryUsageBytes) }} / 
                {{ formatBytes(resourceUsage.memoryLimitBytes) }}
              </p>
              <p><span class="font-medium">Network In:</span> {{ formatBytes(resourceUsage.networkBytesReceived) }}</p>
              <p><span class="font-medium">Network Out:</span> {{ formatBytes(resourceUsage.networkBytesTransmitted) }}</p>
            </div>
            <div>
              <p><span class="font-medium">CPU:</span> {{ Math.round(resourceUsage.cpuUsagePercent || 0) }}%</p>
              <p><span class="font-medium">Processes:</span> {{ resourceUsage.processCount || 'N/A' }}</p>
              <p><span class="font-medium">Uptime:</span> {{ formatDuration(resourceUsage.uptimeSeconds) }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tool Test Modal -->
    <McpToolTestModal 
      v-if="selectedTool"
      :tool="selectedTool"
      :sandbox-id="instance.sandboxId"
      @close="selectedTool = null"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useMcpStore } from '@/modules/mcp/stores/mcp'
import type { McpServerInstance, McpTool, McpServerInfo, SandboxResourceUsage } from '@/modules/mcp/types/mcp'
import McpToolTestModal from './McpToolTestModal.vue'

interface Props {
  instance: McpServerInstance
}

const props = defineProps<Props>()
const emit = defineEmits<{
  stop: [sandboxId: string]
  restart: [sandboxId: string]
  viewLogs: [sandboxId: string]
}>()

const mcpStore = useMcpStore()
const expanded = ref(false)
const loading = ref(false)
const tools = ref<McpTool[]>([])
const serverInfo = ref<McpServerInfo | null>(null)
const resourceUsage = ref<SandboxResourceUsage | null>(null)
const selectedTool = ref<McpTool | null>(null)

let resourceUpdateInterval: number | null = null

onMounted(async () => {
  if (props.instance.status === 'RUNNING') {
    await loadServerData()
    startResourceMonitoring()
  }
})

onUnmounted(() => {
  if (resourceUpdateInterval) {
    clearInterval(resourceUpdateInterval)
  }
})

const loadServerData = async () => {
  try {
    const [toolsData, infoData, resourceData] = await Promise.all([
      mcpStore.getTools(props.instance.sandboxId),
      mcpStore.getServerInfo(props.instance.sandboxId),
      mcpStore.getResourceUsage(props.instance.sandboxId)
    ])
    
    tools.value = toolsData
    serverInfo.value = infoData
    resourceUsage.value = resourceData
  } catch (error) {
    console.error('Failed to load server data:', error)
  }
}

const startResourceMonitoring = () => {
  resourceUpdateInterval = window.setInterval(async () => {
    try {
      resourceUsage.value = await mcpStore.getResourceUsage(props.instance.sandboxId)
    } catch (error) {
      console.error('Failed to update resource usage:', error)
    }
  }, 5000) // Update every 5 seconds
}

const toggleExpanded = () => {
  expanded.value = !expanded.value
  if (expanded.value && !tools.value.length) {
    loadServerData()
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'RUNNING': return 'bg-green-100 text-green-800'
    case 'STARTING': return 'bg-yellow-100 text-yellow-800'
    case 'STOPPING': return 'bg-orange-100 text-orange-800'
    case 'FAILED': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const formatDate = (dateString?: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleString()
}

const formatUptime = (startTime?: string) => {
  if (!startTime) return 'N/A'
  const start = new Date(startTime)
  const now = new Date()
  const diff = now.getTime() - start.getTime()
  return formatDuration(Math.floor(diff / 1000))
}

const formatDuration = (seconds?: number) => {
  if (!seconds) return 'N/A'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`
  } else {
    return `${secs}s`
  }
}

const formatBytes = (bytes?: number) => {
  if (!bytes) return '0 B'
  
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
}

const viewTools = () => {
  expanded.value = true
  if (!tools.value.length) {
    loadServerData()
  }
}

const viewLogs = () => {
  emit('viewLogs', props.instance.sandboxId)
}

const restart = async () => {
  loading.value = true
  try {
    emit('restart', props.instance.sandboxId)
  } finally {
    loading.value = false
  }
}

const stop = async () => {
  loading.value = true
  try {
    emit('stop', props.instance.sandboxId)
  } finally {
    loading.value = false
  }
}

const selectTool = (tool: McpTool) => {
  selectedTool.value = tool
}
</script>
