package com.example.springvueapp.devops.service;

import com.example.springvueapp.devops.entity.DevOpsCdTaskEntity;
import com.example.springvueapp.devops.mapper.DevOpsCdTaskMapper;
import com.example.springvueapp.devops.model.DevOpsCdTask;
import com.example.springvueapp.devops.model.DevOpsCdTaskInstance;
import com.example.springvueapp.devops.repository.DevOpsCdTaskRepository;
import com.example.springvueapp.devops.repository.DevOpsApplicationRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Tekton CD任务服务实现
 * 实现CdTaskService抽象接口，支持部署任务管理、YAML配置生成和部署监控功能
 */
@Service
public class TektonCdTaskService implements CdTaskService {

    private final DevOpsCdTaskRepository cdTaskRepository;
    private final DevOpsApplicationRepository applicationRepository;
    private final DevOpsCdTaskMapper cdTaskMapper;
    private final ObjectMapper objectMapper;

    @Autowired
    public TektonCdTaskService(DevOpsCdTaskRepository cdTaskRepository,
                              DevOpsApplicationRepository applicationRepository,
                              DevOpsCdTaskMapper cdTaskMapper,
                              ObjectMapper objectMapper) {
        this.cdTaskRepository = cdTaskRepository;
        this.applicationRepository = applicationRepository;
        this.cdTaskMapper = cdTaskMapper;
        this.objectMapper = objectMapper;
    }

    @Override
    public Mono<DevOpsCdTask> createCdTask(DevOpsCdTask cdTask, Long applicationId, Long userId) {
        // 首先验证应用是否存在且用户有权限
        return applicationRepository.findByUserIdAndId(userId, applicationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("应用不存在或无权限访问")))
                .flatMap(application -> 
                    cdTaskRepository.existsByApplicationIdAndName(applicationId, cdTask.getName())
                            .flatMap(exists -> {
                                if (exists) {
                                    return Mono.error(new IllegalArgumentException("CD任务名称在该应用中已存在"));
                                }
                                
                                // 创建新的CD任务实体
                                DevOpsCdTaskEntity entity = new DevOpsCdTaskEntity();
                                entity.setName(cdTask.getName());
                                entity.setDescription(cdTask.getDescription());
                                entity.setApplicationId(applicationId);
                                entity.setStatus("INACTIVE");
                                entity.setUserId(userId);
                                entity.setCreatedAt(LocalDateTime.now());
                                entity.setUpdatedAt(LocalDateTime.now());
                                
                                // 序列化组件版本信息
                                try {
                                    if (cdTask.getComponentVersions() != null) {
                                        entity.setComponentVersions(objectMapper.writeValueAsString(cdTask.getComponentVersions()));
                                    }
                                    if (cdTask.getConfiguration() != null) {
                                        entity.setConfiguration(objectMapper.writeValueAsString(cdTask.getConfiguration()));
                                    }
                                } catch (JsonProcessingException e) {
                                    return Mono.error(new IllegalArgumentException("配置信息序列化失败: " + e.getMessage()));
                                }
                                
                                return cdTaskRepository.save(entity);
                            })
                )
                .map(cdTaskMapper::toModel);
    }

    @Override
    public Mono<DevOpsCdTask> updateCdTask(Long taskId, DevOpsCdTask cdTask, Long userId) {
        return cdTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CD任务不存在或无权限访问")))
                .flatMap(existingEntity -> {
                    // 检查名称是否与其他任务冲突
                    if (!existingEntity.getName().equals(cdTask.getName())) {
                        return cdTaskRepository.existsByApplicationIdAndName(existingEntity.getApplicationId(), cdTask.getName())
                                .flatMap(exists -> {
                                    if (exists) {
                                        return Mono.error(new IllegalArgumentException("CD任务名称在该应用中已存在"));
                                    }
                                    return updateEntityFields(existingEntity, cdTask);
                                });
                    } else {
                        return updateEntityFields(existingEntity, cdTask);
                    }
                })
                .flatMap(cdTaskRepository::save)
                .map(cdTaskMapper::toModel);
    }

    private Mono<DevOpsCdTaskEntity> updateEntityFields(DevOpsCdTaskEntity entity, DevOpsCdTask cdTask) {
        entity.setName(cdTask.getName());
        entity.setDescription(cdTask.getDescription());
        entity.setUpdatedAt(LocalDateTime.now());
        
        // 序列化组件版本信息和配置
        try {
            if (cdTask.getComponentVersions() != null) {
                entity.setComponentVersions(objectMapper.writeValueAsString(cdTask.getComponentVersions()));
            }
            if (cdTask.getConfiguration() != null) {
                entity.setConfiguration(objectMapper.writeValueAsString(cdTask.getConfiguration()));
            }
        } catch (JsonProcessingException e) {
            return Mono.error(new IllegalArgumentException("配置信息序列化失败: " + e.getMessage()));
        }
        
        return Mono.just(entity);
    }

    @Override
    public Mono<Boolean> deleteCdTask(Long taskId, Long userId) {
        return cdTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CD任务不存在或无权限访问")))
                .flatMap(entity -> {
                    // 检查任务状态，运行中的任务不能删除
                    if ("RUNNING".equals(entity.getStatus()) || "DEPLOYING".equals(entity.getStatus())) {
                        return Mono.error(new IllegalArgumentException("运行中的CD任务不能删除"));
                    }
                    return cdTaskRepository.delete(entity).then(Mono.just(true));
                })
                .onErrorReturn(false);
    }

    @Override
    public Mono<DevOpsCdTask> getCdTaskById(Long taskId, Long userId) {
        return cdTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CD任务不存在或无权限访问")))
                .map(cdTaskMapper::toModel);
    }

    @Override
    public Flux<DevOpsCdTask> getCdTasksByApplication(Long applicationId, Long userId) {
        return applicationRepository.findByUserIdAndId(userId, applicationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("应用不存在或无权限访问")))
                .flatMapMany(application -> 
                    cdTaskRepository.findByUserIdAndApplicationId(userId, applicationId)
                )
                .map(cdTaskMapper::toModel);
    }

    @Override
    public Flux<DevOpsCdTask> getAllCdTasks(Long userId) {
        return cdTaskRepository.findByUserId(userId)
                .map(cdTaskMapper::toModel);
    }

    @Override
    public Mono<Map<String, Object>> startCdTask(Long taskId, Long userId, Map<String, Object> parameters) {
        return cdTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CD任务不存在或无权限访问")))
                .flatMap(cdTaskEntity -> {
                    // 检查任务状态
                    if ("RUNNING".equals(cdTaskEntity.getStatus()) || "DEPLOYING".equals(cdTaskEntity.getStatus())) {
                        return Mono.error(new IllegalArgumentException("CD任务正在运行中"));
                    }
                    
                    // 更新任务状态为部署中
                    cdTaskEntity.setStatus("DEPLOYING");
                    cdTaskEntity.setUpdatedAt(LocalDateTime.now());
                    
                    return cdTaskRepository.save(cdTaskEntity)
                            .flatMap(savedEntity -> {
                                // 生成部署ID
                                String deploymentId = generateDeploymentId(savedEntity.getName());
                                
                                // 创建部署结果
                                Map<String, Object> result = new HashMap<>();
                                result.put("deploymentId", deploymentId);
                                result.put("status", "DEPLOYING");
                                result.put("startTime", LocalDateTime.now().toString());
                                result.put("taskId", taskId);
                                result.put("parameters", parameters != null ? parameters : new HashMap<>());
                                
                                // 这里应该调用实际的Tekton部署逻辑
                                // 暂时模拟部署过程
                                return simulateDeployment(savedEntity, deploymentId, parameters)
                                        .thenReturn(result);
                            });
                });
    }

    @Override
    public Mono<Boolean> stopCdTask(Long taskId, Long userId) {
        return cdTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CD任务不存在或无权限访问")))
                .flatMap(entity -> {
                    if (!"DEPLOYING".equals(entity.getStatus()) && !"RUNNING".equals(entity.getStatus())) {
                        return Mono.error(new IllegalArgumentException("CD任务未在运行中"));
                    }
                    
                    // 更新任务状态
                    entity.setStatus("STOPPED");
                    entity.setUpdatedAt(LocalDateTime.now());
                    
                    return cdTaskRepository.save(entity)
                            .then(Mono.just(true));
                })
                .onErrorReturn(false);
    }

    @Override
    public Mono<Map<String, Object>> getCdTaskStatus(Long taskId, Long userId) {
        return cdTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CD任务不存在或无权限访问")))
                .map(entity -> {
                    Map<String, Object> status = new HashMap<>();
                    status.put("taskId", entity.getId());
                    status.put("name", entity.getName());
                    status.put("status", entity.getStatus());
                    status.put("lastUpdated", entity.getUpdatedAt().toString());
                    status.put("applicationId", entity.getApplicationId());
                    
                    // 解析组件版本信息
                    try {
                        if (entity.getComponentVersions() != null) {
                            Map<String, String> versions = objectMapper.readValue(
                                entity.getComponentVersions(), 
                                objectMapper.getTypeFactory().constructMapType(Map.class, String.class, String.class)
                            );
                            status.put("componentVersions", versions);
                        }
                    } catch (JsonProcessingException e) {
                        status.put("componentVersions", new HashMap<>());
                    }
                    
                    return status;
                });
    }

    /**
     * 生成部署ID
     */
    private String generateDeploymentId(String taskName) {
        return taskName.toLowerCase().replaceAll("[^a-z0-9]", "-") + "-" + System.currentTimeMillis();
    }

    @Override
    public Flux<Map<String, Object>> getCdTaskDeploymentHistory(Long taskId, Long userId) {
        return cdTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CD任务不存在或无权限访问")))
                .flatMapMany(entity -> {
                    // 模拟部署历史数据
                    Map<String, Object> historyEntry = new HashMap<>();
                    historyEntry.put("deploymentId", "deploy-" + System.currentTimeMillis());
                    historyEntry.put("version", "1.0.0");
                    historyEntry.put("status", "SUCCESS");
                    historyEntry.put("startTime", LocalDateTime.now().minusHours(1).toString());
                    historyEntry.put("endTime", LocalDateTime.now().toString());
                    historyEntry.put("duration", 3600);
                    historyEntry.put("deployedBy", userId);

                    return Flux.just(historyEntry);
                });
    }

    @Override
    public Mono<Map<String, Object>> rollbackCdTask(Long taskId, Long userId, String targetVersion) {
        return cdTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CD任务不存在或无权限访问")))
                .flatMap(entity -> {
                    if (targetVersion == null || targetVersion.trim().isEmpty()) {
                        return Mono.error(new IllegalArgumentException("目标版本不能为空"));
                    }

                    // 更新任务状态为回滚中
                    entity.setStatus("ROLLING_BACK");
                    entity.setUpdatedAt(LocalDateTime.now());

                    return cdTaskRepository.save(entity)
                            .map(savedEntity -> {
                                Map<String, Object> result = new HashMap<>();
                                result.put("taskId", taskId);
                                result.put("targetVersion", targetVersion);
                                result.put("status", "ROLLING_BACK");
                                result.put("rollbackId", "rollback-" + System.currentTimeMillis());
                                result.put("startTime", LocalDateTime.now().toString());

                                return result;
                            });
                });
    }

    @Override
    public Mono<Map<String, Object>> getResourcePackages(Map<String, String> componentVersions) {
        Map<String, Object> packages = new HashMap<>();
        packages.put("components", componentVersions != null ? componentVersions : new HashMap<>());
        packages.put("totalSize", 0L);
        packages.put("packageCount", componentVersions != null ? componentVersions.size() : 0);
        packages.put("lastUpdated", LocalDateTime.now().toString());

        return Mono.just(packages);
    }

    @Override
    public Mono<String> generateDeploymentCommand(DevOpsCdTask cdTask, Map<String, Object> parameters) {
        if (cdTask == null) {
            return Mono.error(new IllegalArgumentException("CD任务不能为空"));
        }

        StringBuilder command = new StringBuilder();
        command.append("kubectl apply -f deployment.yaml");

        if (parameters != null && !parameters.isEmpty()) {
            command.append(" --set ");
            parameters.forEach((key, value) ->
                command.append(key).append("=").append(value).append(",")
            );
            // 移除最后的逗号
            if (command.charAt(command.length() - 1) == ',') {
                command.setLength(command.length() - 1);
            }
        }

        return Mono.just(command.toString());
    }

    @Override
    public Mono<Boolean> validateConfiguration(Map<String, Object> configuration) {
        if (configuration == null || configuration.isEmpty()) {
            return Mono.just(false);
        }

        // 验证必要的配置项
        if (!configuration.containsKey("deploymentStrategy")) {
            return Mono.just(false);
        }

        String strategy = (String) configuration.get("deploymentStrategy");
        if (!"rolling".equals(strategy) && !"blue-green".equals(strategy) && !"canary".equals(strategy)) {
            return Mono.just(false);
        }

        return Mono.just(true);
    }

    @Override
    public Mono<Map<String, Object>> getDeploymentTemplate(String templateType) {
        Map<String, Object> template = new HashMap<>();

        switch (templateType.toLowerCase()) {
            case "rolling":
                template.put("strategy", "RollingUpdate");
                template.put("maxUnavailable", "25%");
                template.put("maxSurge", "25%");
                break;
            case "blue-green":
                template.put("strategy", "BlueGreen");
                template.put("prePromotionAnalysis", true);
                template.put("scaleDownDelaySeconds", 30);
                break;
            case "canary":
                template.put("strategy", "Canary");
                template.put("steps", new String[]{"10%", "50%", "100%"});
                template.put("analysisInterval", "30s");
                break;
            default:
                return Mono.error(new IllegalArgumentException("不支持的模板类型: " + templateType));
        }

        template.put("templateType", templateType);
        template.put("createdAt", LocalDateTime.now().toString());

        return Mono.just(template);
    }

    @Override
    public Flux<String> getSupportedDeploymentTypes() {
        return Flux.just("rolling", "blue-green", "canary", "recreate");
    }

    @Override
    public Mono<Boolean> checkConnection() {
        // 模拟检查Tekton连接状态
        return Mono.just(true);
    }

    @Override
    public Mono<Map<String, Object>> getPlatformInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("platform", "Tekton");
        info.put("version", "0.50.0");
        info.put("status", "Connected");
        info.put("namespace", "default");
        info.put("supportedFeatures", new String[]{"Pipeline", "Task", "PipelineRun", "TaskRun"});
        info.put("lastChecked", LocalDateTime.now().toString());

        return Mono.just(info);
    }

    @Override
    public Mono<Boolean> stopCdTaskInstance(String instanceId, Long userId) {
        // TODO: 实现停止CD任务实例的逻辑
        // 这里应该调用Tekton API停止指定的部署实例
        return Mono.just(true);
    }

    @Override
    public Mono<Boolean> cancelCdTaskInstance(String instanceId, Long userId) {
        // TODO: 实现取消CD任务实例的逻辑
        // 这里应该调用Tekton API取消指定的部署实例
        return Mono.just(true);
    }

    @Override
    public Mono<DevOpsCdTaskInstance> getCdTaskInstanceStatus(String instanceId, Long userId) {
        // TODO: 实现获取CD任务实例状态的逻辑
        // 这里应该调用Tekton API获取指定实例的状态
        DevOpsCdTaskInstance instance = new DevOpsCdTaskInstance();
        instance.setInstanceId(instanceId);
        instance.setStatus("RUNNING");
        instance.setStartTime(LocalDateTime.now().minusMinutes(10));
        instance.setTargetEnvironment("production");
        instance.setDeploymentStrategy("rolling_update");
        instance.setVersion("1.0.0");
        instance.setUserId(userId);
        instance.setCreatedAt(LocalDateTime.now().minusMinutes(10));
        instance.setUpdatedAt(LocalDateTime.now());

        return Mono.just(instance);
    }

    @Override
    public Flux<DevOpsCdTaskInstance> getCdTaskInstances(Long taskId, Long userId) {
        // TODO: 实现获取CD任务所有实例的逻辑
        // 这里应该调用Tekton API获取指定任务的所有实例
        return Flux.empty();
    }

    @Override
    public Mono<String> getCdTaskInstanceLogs(String instanceId, Long userId) {
        // TODO: 实现获取CD任务实例日志的逻辑
        // 这里应该调用Tekton API获取指定实例的日志
        String sampleLogs = "=== CD任务实例日志 ===\n" +
                "开始部署...\n" +
                "正在拉取镜像...\n" +
                "镜像拉取完成\n" +
                "正在创建部署资源...\n" +
                "部署资源创建完成\n" +
                "正在等待Pod就绪...\n" +
                "Pod就绪检查完成\n" +
                "部署成功完成\n";

        return Mono.just(sampleLogs);
    }

    @Override
    public Mono<Integer> cleanupCompletedInstances(Long taskId, Long userId, int keepCount) {
        // TODO: 实现清理已完成实例的逻辑
        // 这里应该调用Tekton API清理指定任务的已完成实例
        return Mono.just(0);
    }

    /**
     * 模拟部署过程
     */
    private Mono<Void> simulateDeployment(DevOpsCdTaskEntity entity, String deploymentId, Map<String, Object> parameters) {
        // 这里应该实现实际的Tekton部署逻辑
        // 暂时返回空的Mono表示部署已启动
        return Mono.empty();
    }
}
