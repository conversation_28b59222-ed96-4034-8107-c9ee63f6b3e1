package com.example.springvueapp.devops.model;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * DevOps CD任务实例的DTO类
 * 与前端TypeScript接口保持一致，用于API交互
 */
public class DevOpsCdTaskInstance {

    private Long id;

    private Long cdTaskId;

    private String instanceId;

    private String status;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String logs;

    private Map<String, Object> deploymentData; // 部署结果数据

    private String errorMessage;

    private String targetEnvironment; // 目标环境

    private String deploymentStrategy; // 部署策略

    private String version; // 部署版本

    private Long userId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public DevOpsCdTaskInstance() {
    }

    public DevOpsCdTaskInstance(Long id, Long cdTaskId, String instanceId, String status,
                               LocalDateTime startTime, LocalDateTime endTime, String logs,
                               Map<String, Object> deploymentData, String errorMessage,
                               String targetEnvironment, String deploymentStrategy, String version,
                               Long userId, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.cdTaskId = cdTaskId;
        this.instanceId = instanceId;
        this.status = status;
        this.startTime = startTime;
        this.endTime = endTime;
        this.logs = logs;
        this.deploymentData = deploymentData;
        this.errorMessage = errorMessage;
        this.targetEnvironment = targetEnvironment;
        this.deploymentStrategy = deploymentStrategy;
        this.version = version;
        this.userId = userId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCdTaskId() {
        return cdTaskId;
    }

    public void setCdTaskId(Long cdTaskId) {
        this.cdTaskId = cdTaskId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getLogs() {
        return logs;
    }

    public void setLogs(String logs) {
        this.logs = logs;
    }

    public Map<String, Object> getDeploymentData() {
        return deploymentData;
    }

    public void setDeploymentData(Map<String, Object> deploymentData) {
        this.deploymentData = deploymentData;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getTargetEnvironment() {
        return targetEnvironment;
    }

    public void setTargetEnvironment(String targetEnvironment) {
        this.targetEnvironment = targetEnvironment;
    }

    public String getDeploymentStrategy() {
        return deploymentStrategy;
    }

    public void setDeploymentStrategy(String deploymentStrategy) {
        this.deploymentStrategy = deploymentStrategy;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "DevOpsCdTaskInstance{" +
                "id=" + id +
                ", cdTaskId=" + cdTaskId +
                ", instanceId='" + instanceId + '\'' +
                ", status='" + status + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", targetEnvironment='" + targetEnvironment + '\'' +
                ", deploymentStrategy='" + deploymentStrategy + '\'' +
                ", version='" + version + '\'' +
                ", userId=" + userId +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
