# 开发运维平台

一个基于 Spring Boot 3.x + Vue.js 3 的现代化响应式开发运维平台，支持AI工具管理和DevOps项目生命周期管理解决方案。

## 🚀 项目概述

本平台是一个企业级的开发运维系统，采用前后端分离的微服务架构，提供：

### 核心功能
- **🔐 用户认证与授权**: 基于JWT的安全认证体系
- **🛠️ MCP协议代理**: 完整的JSON-RPC 2.0协议实现，支持HTTP-to-MCP转换
- **🐳 Docker沙箱环境**: 安全隔离的容器化执行环境
- **⚙️ 配置管理**: 可视化的MCP服务器配置和实例管理
- **📊 DevOps项目管理**: 完整的项目、应用、组件生命周期管理
- **🔄 CI/CD集成**: 支持Tekton的持续集成和持续部署
- **📈 实时监控**: SSE事件流和资源使用监控
- **🌐 响应式架构**: 基于Spring WebFlux的高性能非阻塞I/O

### 技术特色
- **响应式编程**: 全栈响应式架构，支持高并发和低延迟
- **容器化部署**: 完整的Docker容器化解决方案
- **现代化前端**: Vue 3 + TypeScript + Composition API
- **安全隔离**: 多层安全防护和沙箱隔离
- **可扩展架构**: 插件化设计，支持多种沙箱环境和CI/CD平台

## 🏗️ 系统架构

### 技术架构图

```mermaid
graph TB
    subgraph "前端层 (Frontend)"
        A[Vue.js 3 + TypeScript]
        B[Pinia 状态管理]
        C[Vue Router 路由]
        D[Tailwind CSS 样式]
    end

    subgraph "网关层 (Gateway)"
        E[Nginx 反向代理]
        F[SSL/TLS 终端]
    end

    subgraph "应用层 (Backend)"
        G[Spring Boot 3.x]
        H[Spring WebFlux]
        I[Spring Security]
        J[JWT 认证]
    end

    subgraph "业务服务层 (Services)"
        K[MCP配置服务]
        L[MCP代理服务]
        M[DevOps管理服务]
        N[沙箱管理服务]
    end

    subgraph "数据存储层 (Storage)"
        O[(H2/PostgreSQL)]
        P[(Redis 缓存)]
    end

    subgraph "容器化层 (Container)"
        Q[Docker Engine]
        R[MCP服务器容器]
    end

    A --> E
    E --> G
    G --> H
    H --> K
    H --> L
    H --> M
    H --> N
    K --> O
    L --> O
    M --> O
    N --> Q
    Q --> R
```

### 核心组件

#### 1. 前端架构 (Vue.js 3)
- **组件化设计**: 可复用的Vue组件库
- **状态管理**: Pinia集中式状态管理
- **类型安全**: TypeScript完整类型检查
- **响应式UI**: Tailwind CSS原子化样式

#### 2. 后端架构 (Spring Boot 3.x)
- **响应式Web**: Spring WebFlux非阻塞I/O
- **安全认证**: Spring Security + JWT
- **数据访问**: Spring Data R2DBC响应式数据库访问
- **容器管理**: Docker Java API集成

#### 3. MCP协议层
- **JSON-RPC 2.0**: 完整的MCP协议实现
- **HTTP转换**: RESTful API到MCP协议的转换
- **SSE通信**: Server-Sent Events实时事件流
- **沙箱隔离**: Docker容器安全执行环境

#### 4. DevOps管理层
- **项目管理**: 多层级项目组织结构
- **CI/CD集成**: Tekton平台集成
- **监控告警**: 实时资源监控和状态追踪
- **配置管理**: 可视化配置和模板管理

## 🎯 主要功能模块

### 1. 用户认证与授权模块
- **🔐 JWT认证**: 基于JWT令牌的安全认证体系
- **👤 用户管理**: 用户注册、登录、权限管理
- **🛡️ 安全控制**: Spring Security集成，细粒度权限控制
- **🔄 会话管理**: 自动令牌刷新和过期处理

### 2. MCP服务器管理模块
- **⚙️ 配置管理**: 可视化的MCP服务器配置界面
- **🚀 实例管理**: 服务器实例的启动、停止、监控
- **🔧 工具测试**: 内置的MCP工具测试和调试功能
- **📊 状态监控**: 实时的服务器状态和资源使用监控

### 3. MCP协议代理模块
- **🌐 HTTP代理**: HTTP请求到MCP协议的无缝转换
- **📡 SSE通信**: Server-Sent Events实时事件流
- **🔄 JSON-RPC**: 完整的JSON-RPC 2.0协议实现
- **⚡ 响应式处理**: 基于Spring WebFlux的非阻塞处理

### 4. Docker沙箱环境模块
- **🐳 容器管理**: Docker容器的生命周期管理
- **🔒 安全隔离**: 多层安全防护和资源隔离
- **📈 资源限制**: CPU、内存、网络的精确控制
- **💾 数据持久化**: 灵活的卷挂载和数据管理

### 5. DevOps项目管理模块
- **📁 项目管理**: 多层级的项目组织和管理
- **🏗️ 应用管理**: 应用的创建、配置和部署管理
- **🔧 组件管理**: 代码组件的版本控制和构建管理
- **📊 资源管理**: 基础设施资源的统一管理

### 6. CI/CD管理模块
- **🔄 CI管理**: 持续集成任务的创建、配置和执行管理
- **🚀 CD管理**: 持续部署任务的创建、配置和部署管理
- **📈 任务监控**: 实时的构建和部署状态监控
- **📋 日志管理**: 完整的构建和部署日志追踪

### 7. 监控中心模块
- **📊 实时监控**: 系统资源和服务状态的实时监控
- **🚨 告警管理**: 智能的告警规则和通知机制
- **📈 性能分析**: 详细的性能指标和趋势分析
- **📋 审计日志**: 完整的操作审计和日志记录

## API 端点

### 用户认证

```
POST   /auth/login                       # 用户登录
POST   /auth/register                    # 用户注册
GET    /users/{username}                 # 根据用户名查找用户
GET    /users/search                     # 搜索用户
POST   /users                           # 创建或更新用户
```

### 配置管理

```
GET    /api/mcp/configurations           # 列出用户配置
POST   /api/mcp/configurations           # 创建新配置
GET    /api/mcp/configurations/{id}      # 获取特定配置
PUT    /api/mcp/configurations/{id}      # 更新配置
DELETE /api/mcp/configurations/{id}      # 删除配置
POST   /api/mcp/configurations/{id}/toggle # 切换启用状态
```

### 服务器管理

```
POST   /api/mcp/proxy/start/{configId}   # 启动 MCP 服务器实例
POST   /api/mcp/proxy/stop/{sandboxId}   # 停止 MCP 服务器实例
GET    /api/mcp/proxy/instances          # 列出运行中的实例
GET    /api/mcp/proxy/instances/{id}/status    # 获取实例状态
GET    /api/mcp/proxy/instances/{id}/resources # 获取资源使用情况
```

### MCP 操作

```
GET    /api/mcp/proxy/{sandboxId}/tools         # 列出可用工具
GET    /api/mcp/proxy/{sandboxId}/info          # 获取服务器信息
POST   /api/mcp/proxy/{sandboxId}/tools/{tool}  # 调用特定工具
POST   /api/mcp/proxy/{sandboxId}/http/{tool}   # HTTP-to-MCP 代理
```

## 使用流程

### 1. 用户注册和登录

1. **访问应用**: 打开浏览器访问 `http://localhost:3000`
2. **用户注册**: 如果是新用户，点击"注册"按钮创建账户
3. **用户登录**: 使用用户名和密码登录系统
4. **自动跳转**: 登录成功后自动跳转到 MCP 配置管理界面

### 2. MCP 服务器配置

1. **创建配置**: 在 MCP 仪表板中点击"添加新服务器"
2. **填写配置**:
   - 服务器名称和描述
   - Docker 镜像（如 `node:18-alpine`）
   - 启动命令和参数
   - 环境变量
   - 资源限制
3. **保存配置**: 验证并保存配置

### 3. 启动和管理 MCP 服务器

1. **启动服务器**: 在配置列表中点击"启动"按钮
2. **监控状态**: 实时查看服务器状态和资源使用情况
3. **测试工具**: 使用内置的工具测试界面验证功能
4. **查看日志**: 监控服务器运行日志和错误信息

### 4. 使用 MCP 服务

1. **发现工具**: 查看服务器提供的可用工具
2. **调用工具**: 通过 HTTP API 或 Web 界面调用工具
3. **监控性能**: 查看工具调用的响应时间和成功率

## 安全考虑

### 沙箱安全

- **容器隔离**: 每个 MCP 服务器在自己的 Docker 容器中运行
- **资源限制**: 防止资源耗尽攻击
- **网络隔离**: 默认网络隔离，可配置访问
- **用户权限**: 容器内非 root 执行

### 访问控制

- **用户范围**: 配置和实例限定在已认证用户范围内
- **身份验证**: 与 Spring Security 集成
- **授权**: 管理功能的基于角色的访问控制

### 数据保护

- **输入验证**: 所有输入的全面验证
- **错误处理**: 安全的错误消息，不泄露信息
- **审计日志**: 所有操作的完整审计跟踪

## 部署

### 前提条件

- Java 17+
- Docker Engine
- Node.js 18+ (用于前端)
- Maven 3.6+

### 后端设置

```bash
cd backend
mvn clean install
mvn spring-boot:run
```

### 前端设置

```bash
cd frontend
npm install
npm run dev
```

## 扩展点

### 添加新的沙箱环境

1. 实现 `SandboxEnvironment` 接口
2. 创建相应的 `SandboxInstance` 实现
3. 在 `McpConfig` 中添加配置 bean
4. 更新应用程序属性

Kubernetes 示例：

```java
@Component
@ConditionalOnProperty(name = "mcp.sandbox.type", havingValue = "kubernetes")
public class KubernetesSandboxEnvironment implements SandboxEnvironment {
    // Kubernetes pods 的实现
}
```

## 项目结构

```
mcp-gw/
├── .devcontainer/                      # 开发容器配置
│   ├── Dockerfile                      # 容器构建文件
│   └── devcontainer.json               # 开发容器配置
├── .gitignore                          # Git 忽略文件
├── README.md                           # 项目文档
├── backend/                            # Spring Boot 后端
│   ├── .mvn/                           # Maven 包装器
│   │   └── wrapper/
│   │       ├── maven-wrapper.jar       # Maven 包装器JAR
│   │       └── maven-wrapper.properties # 包装器配置
│   ├── Dockerfile                      # 后端 Docker 构建文件
│   ├── Dockerfile.native               # 原生镜像构建文件
│   ├── GRAALVM-VERIFICATION.md         # GraalVM 验证文档
│   ├── README-GRAALVM.md               # GraalVM 说明文档
│   ├── build-native.sh                 # 原生构建脚本
│   ├── mvnw                            # Maven 包装器脚本
│   ├── mvnw.cmd                        # Windows Maven 包装器
│   ├── pom.xml                         # Maven 项目配置
│   ├── src/main/java/com/example/springvueapp/
│   │   ├── common/                     # 通用组件
│   │   │   ├── config/                 # 通用配置
│   │   │   ├── controller/             # 通用控制器
│   │   │   ├── entity/                 # 通用实体
│   │   │   ├── filter/                 # 过滤器
│   │   │   ├── mapper/                 # 数据映射
│   │   │   ├── model/                  # 通用模型
│   │   │   ├── repository/             # 通用仓库
│   │   │   └── service/                # 通用服务
│   │   ├── devops/                     # DevOps相关功能
│   │   │   ├── controller/             # DevOps控制器
│   │   │   ├── entity/                 # DevOps实体
│   │   │   ├── mapper/                 # DevOps数据映射
│   │   │   ├── model/                  # DevOps模型
│   │   │   ├── repository/             # DevOps仓库
│   │   │   └── service/                # DevOps服务

│   │   ├── mcp/                        # MCP协议实现
│   │   │   ├── client/                 # MCP客户端实现
│   │   │   ├── config/                 # MCP配置
│   │   │   ├── controller/             # MCP控制器
│   │   │   ├── entity/                 # MCP实体
│   │   │   ├── mapper/                 # MCP数据映射
│   │   │   ├── model/                  # MCP数据模型定义
│   │   │   ├── protocol/               # JSON-RPC协议处理
│   │   │   ├── repository/             # MCP仓库
│   │   │   └── service/                # MCP服务
│   │   ├── sandbox/                    # 沙箱环境
│   │   │   ├── SandboxConfig.java      # 沙箱配置
│   │   │   ├── SandboxEnvironment.java # 沙箱环境
│   │   │   ├── SandboxInstance.java     # 沙箱实例
│   │   │   ├── SandboxResourceUsage.java # 资源使用
│   │   │   ├── SandboxStatus.java       # 沙箱状态
│   │   │   ├── docker/                 # Docker相关配置
│   │   │   └── model/                  # 沙箱模型定义
│   │   └── SpringVueAppApplication.java # 应用入口类
│   ├── src/main/resources/
│   │   ├── application.properties      # 应用配置
│   │   ├── openapi.yaml                # OpenAPI规范
│   │   └── schema.sql                  # 数据库架构脚本
│   └── src/test/                       # 单元测试代码
│       ├── java/com/example/springvueapp/
│       │   ├── common/                 # 通用组件测试
│       │   │   ├── config/
│       │   │   ├── controller/
│       │   │   ├── repository/
│       │   │   └── service/
│       │   ├── controller/             # 控制器测试
│       │   │   ├── AuthControllerTest.java
│       │   │   └── McpProxyControllerTest.java
│       │   ├── devops/                 # DevOps测试
│       │   │   ├── controller/
│       │   │   ├── mapper/
│       │   │   └── service/
│       │   ├── integration/            # 集成测试
│       │   │   ├── DevOpsIntegrationTest.java
│       │   │   ├── McpSseIntegrationTest.java
│       │   │   └── VolumeAndHostIntegrationTest.java
│       │   ├── mcp/                    # MCP协议测试
│       │   │   ├── controller/
│       │   │   ├── mapper/
│       │   │   ├── model/
│       │   │   └── service/
│       │   ├── repository/             # 仓库测试
│       │   ├── sandbox/                # 沙箱测试
│       │   │   ├── SandboxConfigTest.java
│       │   │   └── docker/
│       │   ├── service/                # 服务测试
│       │   └── config/                 # 配置测试
│       └── resources/                  # 测试资源
│           └── application-test.properties
│           ├── controller/
│           │   ├── AuthControllerTest.java
│           │   └── McpProxyControllerTest.java
│           ├── service/
│           ├── mcp/
│           ├── repository/
│           └── config/
├── docs/                               # 项目文档
│   └── PRD-产品需求文档.md              # 产品需求文档
├── frontend/                           # Vue.js 前端
│   ├── Dockerfile                      # 前端 Docker 构建文件
│   ├── index.html                      # 入口 HTML 文件
│   ├── nginx.conf                      # Nginx 配置
│   ├── package-lock.json               # NPM 依赖锁定
│   ├── package.json                    # NPM 项目配置
│   ├── pnpm-lock.yaml                  # PNPM 依赖锁定
│   ├── postcss.config.js               # PostCSS 配置
│   ├── src/
│   │   ├── App.vue                     # 根组件
│   │   ├── __mocks__/                  # 测试模拟数据
│   │   ├── assets/                     # 静态资源
│   │   │   └── styles/                 # 样式文件
│   │   │       ├── base.css            # 基础样式
│   │   │       ├── components.css       # 组件样式
│   │   │       ├── index.css           # 样式入口
│   │   │       ├── layout.css          # 布局样式
│   │   │       └── utilities.css       # 工具样式
│   │   ├── components/                 # Vue 组件
│   │   │   ├── Breadcrumb.vue          # 面包屑组件
│   │   │   ├── ConfirmDialog.vue       # 确认对话框
│   │   │   ├── DataTable.vue           # 数据表格
│   │   │   ├── FormField.vue           # 表单字段
│   │   │   ├── StatusTag.vue           # 状态标签
│   │   │   ├── YamlEditor.vue          # YAML编辑器
│   │   │   └── __tests__/              # 组件测试
│   │   │       ├── DataTable.test.ts   # 数据表格测试
│   │   │       ├── StatusTag.test.ts   # 状态标签测试
│   │   │       └── YamlEditor.test.ts  # YAML编辑器测试
│   ├── modules/                      # 业务模块
│   │   ├── common/                 # 通用模块
│   │   │   ├── stores/             # 状态管理
│   │   │   ├── types/              # 类型定义
│   │   │   └── views/              # 视图组件
│   │   ├── devops/                 # DevOps模块
│   │   │   ├── components/         # 组件
│   │   │   ├── services/           # 服务
│   │   │   │   └── devopsApi.ts    # DevOps API服务
│   │   │   ├── stores/             # 状态管理
│   │   │   │   ├── ci.ts           # CI任务状态管理
│   │   │   │   ├── cd.ts           # CD任务状态管理
│   │   │   │   └── devops.ts       # DevOps通用状态管理
│   │   │   ├── types/              # 类型定义
│   │   │   │   └── devops.ts       # DevOps类型定义
│   │   │   ├── utils/              # 工具函数
│   │   │   └── views/              # 视图组件
│   │   │       ├── ApplicationList.vue    # 应用管理
│   │   │       ├── CDManagement.vue       # CD管理（持续部署）
│   │   │       ├── CIManagement.vue       # CI管理（持续集成）
│   │   │       ├── ComponentList.vue      # 组件管理
│   │   │       ├── DevOpsDashboard.vue    # DevOps仪表板
│   │   │       ├── MonitoringCenter.vue   # 监控中心
│   │   │       ├── ProjectList.vue        # 项目管理
│   │   │       └── ResourceList.vue       # 资源管理
│   │   └── mcp/                    # MCP模块
│   │       ├── components/         # 组件
│   │       ├── stores/             # 状态管理
│   │       ├── types/              # 类型定义
│   │       └── views/              # 视图组件


│   │   │   ├── __tests__/              # 组件测试


│   │   ├── env.d.ts                    # 环境类型定义
│   │   ├── main.ts                     # 入口脚本
│   │   ├── router/                     # 路由配置
│   │   │   └── index.ts                # 路由入口文件，定义路由规则和守卫
│   │   ├── shims-vue.d.ts              # Vue 类型声明
│   │   ├── stores/                     # Pinia 状态存储
│   │   │   ├── auth.ts                 # 认证状态
│   │   │   ├── mcp.ts                  # MCP 状态
│   │   │   └── ui.ts                   # UI状态管理
│   │   ├── types/                      # TypeScript 类型
│   │   │   └── mcp.ts                  # MCP 类型定义
│   │   ├── utils/                      # 工具函数
│   │   │   └── axios.ts                # HTTP客户端
│   │   └── views/                      # 页面组件
│   │       ├── LoginView.vue           # 登录页面
│   │       ├── HomeView.vue            # 主页
│   │       └── McpDashboard.vue        # MCP 仪表板
│   ├── tailwind.config.js              # Tailwind CSS 配置
│   ├── tsconfig.json                   # TypeScript 配置
│   ├── tsconfig.node.json              # Node TypeScript 配置
│   ├── vite.config.ts                  # Vite 配置
│   └── vitest.config.ts                # Vitest 测试配置
└──├── docker-compose.yml                  # Docker Compose 配置

```

## 💻 技术栈

### 后端技术栈
| 技术 | 版本 | 用途 |
|------|------|------|
| **Spring Boot** | 3.2+ | 应用框架，支持GraalVM Native Image |
| **Spring WebFlux** | 6.0+ | 响应式Web框架，非阻塞I/O |
| **Spring Security** | 6.0+ | 安全框架，JWT认证 |
| **Spring Data R2DBC** | 3.0+ | 响应式数据访问层 |
| **Java** | 17+ | 编程语言 |
| **Maven** | 3.8+ | 构建工具和依赖管理 |
| **H2/PostgreSQL** | - | 开发/生产环境数据库 |
| **Docker Java API** | - | 容器管理和沙箱环境 |
| **JSON-RPC 2.0** | - | MCP协议实现 |
| **JWT** | - | 身份验证令牌 |

### 前端技术栈
| 技术 | 版本 | 用途 |
|------|------|------|
| **Vue.js** | 3.3+ | 渐进式JavaScript框架 |
| **TypeScript** | 5.0+ | 类型安全的JavaScript超集 |
| **Vite** | 4.0+ | 现代化构建工具 |
| **Pinia** | 2.0+ | 状态管理库 |
| **Vue Router** | 4.0+ | 单页应用路由 |
| **Tailwind CSS** | 3.0+ | 原子化CSS框架 |
| **Axios** | 1.6+ | HTTP客户端库 |
| **Monaco Editor** | - | 代码编辑器组件 |

### 基础设施技术栈
| 技术 | 版本 | 用途 |
|------|------|------|
| **Docker** | 20.0+ | 容器化平台 |
| **Docker Compose** | 2.0+ | 多容器编排 |
| **Nginx** | 1.20+ | 反向代理和负载均衡 |
| **Redis** | 6.0+ | 缓存和会话存储 |
| **Tekton** | 0.50+ | Kubernetes原生CI/CD |
| **GraalVM** | 17+ | 原生镜像编译 |
